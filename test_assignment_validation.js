#!/usr/bin/env node

/**
 * Test Script: Assignment Validation Fix
 * 
 * This script tests the fixes for the exception handling system
 * to ensure trucks with valid assignments don't trigger false exceptions.
 */

const { getClient } = require('./server/config/database');

async function testAssignmentValidation() {
  console.log('🔍 Testing Assignment Validation Fixes...\n');
  
  const client = await getClient();
  
  try {
    // Test 1: Check current assignments for DT-100
    console.log('📋 Test 1: Current Assignments for DT-100');
    const assignmentsResult = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.assigned_date,
        ll.name as loading_location,
        ul.name as unloading_location,
        (SELECT COUNT(*) FROM trip_logs tl WHERE tl.assignment_id = a.id) as trip_count
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1 AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `);
    
    console.log(`Found ${assignmentsResult.rows.length} active assignments:`);
    assignmentsResult.rows.forEach(assignment => {
      console.log(`  ✅ ${assignment.assignment_code}: ${assignment.loading_location} → ${assignment.unloading_location} (${assignment.trip_count} trips)`);
    });
    
    // Test 2: Check for pending exceptions
    console.log('\n🚨 Test 2: Pending Exceptions for DT-100');
    const pendingExceptionsResult = await client.query(`
      SELECT 
        ap.id,
        ap.exception_type,
        ap.status,
        ap.created_at,
        tl.id as trip_id,
        tl.status as trip_status
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 AND ap.status = 'pending'
      ORDER BY ap.created_at DESC
    `);
    
    if (pendingExceptionsResult.rows.length > 0) {
      console.log(`⚠️  Found ${pendingExceptionsResult.rows.length} pending exceptions:`);
      pendingExceptionsResult.rows.forEach(exception => {
        console.log(`  🔴 Exception ${exception.id}: ${exception.exception_type} (Trip ${exception.trip_id}: ${exception.trip_status})`);
      });
    } else {
      console.log('✅ No pending exceptions found');
    }
    
    // Test 3: Simulate assignment validation for Point A
    console.log('\n🎯 Test 3: Assignment Validation for Point A (Location ID: 1)');
    const validationResult = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location,
        ul.name as unloading_location,
        CASE
          WHEN a.loading_location_id = 1 THEN 'loading'
          WHEN a.unloading_location_id = 1 THEN 'unloading'
          ELSE 'none'
        END as location_role
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1 
        AND a.status IN ('assigned', 'in_progress')
        AND (a.loading_location_id = 1 OR a.unloading_location_id = 1)
      ORDER BY a.created_at DESC
    `);
    
    if (validationResult.rows.length > 0) {
      console.log(`✅ Found ${validationResult.rows.length} valid assignments for Point A:`);
      validationResult.rows.forEach(assignment => {
        console.log(`  ✅ ${assignment.assignment_code}: Point A is ${assignment.location_role} location`);
      });
    } else {
      console.log('❌ No valid assignments found for Point A - this would trigger an exception');
    }
    
    // Test 4: Check getCurrentTripAndAssignment logic
    console.log('\n🔄 Test 4: Current Trip and Assignment Logic');
    const currentTripResult = await client.query(`
      WITH current_trip AS (
        SELECT tl.*, tl.assignment_id as trip_assignment_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = 1
          AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
        ORDER BY tl.created_at DESC
        LIMIT 1
      )
      SELECT 
        ct.id as trip_id,
        ct.status as trip_status,
        ct.trip_assignment_id,
        a.assignment_code
      FROM current_trip ct
      LEFT JOIN assignments a ON ct.trip_assignment_id = a.id
    `);
    
    if (currentTripResult.rows.length > 0) {
      const trip = currentTripResult.rows[0];
      console.log(`🔄 Active trip found: Trip ${trip.trip_id} (${trip.trip_status}) on Assignment ${trip.assignment_code}`);
    } else {
      console.log('✅ No active trips found - assignment validation will work correctly');
    }
    
    // Test 5: Summary and recommendations
    console.log('\n📊 Test Summary:');
    const hasMultipleAssignments = assignmentsResult.rows.length > 1;
    const hasPendingExceptions = pendingExceptionsResult.rows.length > 0;
    const hasValidAssignmentForPointA = validationResult.rows.length > 0;
    const hasActiveTripBlocking = currentTripResult.rows.length > 0;
    
    console.log(`  Multiple Assignments: ${hasMultipleAssignments ? '✅ Yes' : '❌ No'}`);
    console.log(`  Pending Exceptions: ${hasPendingExceptions ? '🔴 Yes' : '✅ No'}`);
    console.log(`  Valid Assignment for Point A: ${hasValidAssignmentForPointA ? '✅ Yes' : '❌ No'}`);
    console.log(`  Active Trip Blocking: ${hasActiveTripBlocking ? '🔴 Yes' : '✅ No'}`);
    
    if (hasValidAssignmentForPointA && !hasActiveTripBlocking) {
      console.log('\n🎉 RESULT: DT-100 scanning at Point A should NOT trigger an exception!');
    } else {
      console.log('\n⚠️  RESULT: Issues detected that may cause false exceptions:');
      if (!hasValidAssignmentForPointA) {
        console.log('   - No valid assignment found for Point A');
      }
      if (hasActiveTripBlocking) {
        console.log('   - Active trip is blocking assignment validation');
      }
      if (hasPendingExceptions) {
        console.log('   - Pending exceptions need to be resolved');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testAssignmentValidation()
    .then(() => {
      console.log('\n✅ Test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testAssignmentValidation };
