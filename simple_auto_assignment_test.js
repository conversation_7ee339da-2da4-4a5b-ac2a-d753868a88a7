#!/usr/bin/env node

/**
 * Simple Auto-Assignment Test
 * 
 * Basic test to verify auto-assignment creation functionality
 */

const { getClient } = require('./server/config/database');
const { AutoAssignmentCreator } = require('./server/utils/AutoAssignmentCreator');

async function simpleAutoAssignmentTest() {
  console.log('🧪 Simple Auto-Assignment Creation Test\n');
  
  const client = await getClient();
  
  try {
    // Get DT-100 truck
    const truckResult = await client.query(`
      SELECT id, truck_number, license_plate, status
      FROM dump_trucks
      WHERE truck_number = 'DT-100'
    `);
    
    const truck = truckResult.rows[0];
    console.log(`✅ Truck: ${truck.truck_number} (ID: ${truck.id})`);
    
    // Get Point C - Secondary Dump Site (should be unassigned)
    const locationResult = await client.query(`
      SELECT id, location_code, name, type
      FROM locations
      WHERE name = 'Point C - Secondary Dump Site'
    `);
    
    const location = locationResult.rows[0];
    console.log(`✅ Location: ${location.name} (ID: ${location.id}, Type: ${location.type})`);
    
    // Test auto-assignment creation
    const autoAssignmentCreator = new AutoAssignmentCreator();
    
    console.log('\n🔍 Checking eligibility...');
    const eligibilityCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
      truck,
      location,
      client
    });
    
    console.log(`Should create: ${eligibilityCheck.shouldCreate}`);
    console.log(`Reason: ${eligibilityCheck.reason}`);
    
    if (eligibilityCheck.shouldCreate) {
      console.log('\n🎯 Creating auto-assignment...');
      
      const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
        truck,
        location,
        client,
        userId: 1
      });
      
      console.log(`✅ Success! Created assignment ${autoAssignment.assignment_code}`);
      console.log(`   Route: ${autoAssignment.loading_location_name} → ${autoAssignment.unloading_location_name}`);
      console.log(`   Status: ${autoAssignment.status}`);
    }
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    client.release();
  }
}

simpleAutoAssignmentTest();
