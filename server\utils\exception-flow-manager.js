/**
 * Optimized Exception Flow Manager
 *
 * This module manages the complete exception flow lifecycle with improved performance:
 * 1. Simplified exception detection and trip status management
 * 2. Streamlined approval workflow with reduced complexity
 * 3. Optimized database queries and transaction handling
 * 4. Enhanced error handling and recovery mechanisms
 * 5. Performance target: <300ms for all operations
 */

const { getClient } = require('../config/database');

/**
 * Exception Flow States
 */
const EXCEPTION_STATES = {
  PENDING: 'exception_pending',
  APPROVED: 'approved', 
  REJECTED: 'rejected'
};

const TRIP_STATES = {
  ASSIGNED: 'assigned',
  LOADING_START: 'loading_start',
  LOADING_END: 'loading_end',
  UNLOADING_START: 'unloading_start',
  UNLOADING_END: 'unloading_end',
  TRIP_COMPLETED: 'trip_completed',
  EXCEPTION_PENDING: 'exception_pending',
  CANCELLED: 'cancelled'
};

/**
 * Enhanced logging for exception flow
 */
function logExceptionFlow(context, message, data = {}) {
  console.log(`[${new Date().toISOString()}] [EXCEPTION_FLOW] [${context}] ${message}`, 
    JSON.stringify(data, null, 2));
}

function logExceptionError(context, error, additionalData = {}) {
  console.error(`[${new Date().toISOString()}] [EXCEPTION_FLOW] [${context}] ERROR:`, {
    message: error.message,
    stack: error.stack,
    ...additionalData
  });
}

/**
 * Check if trip can progress based on current state and approval status
 */
async function validateTripProgression(client, tripLogId) {
  try {
    // Get trip details with approval status
    const tripResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.is_exception,
        tl.exception_approved_at,
        tl.exception_approved_by,
        tl.assignment_id,
        tl.notes,
        a.id as approval_id,
        a.status as approval_status,
        a.exception_type,
        a.reviewed_at,
        a.reviewed_by
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id AND a.status = 'pending'
      WHERE tl.id = $1
    `, [tripLogId]);

    if (tripResult.rows.length === 0) {
      throw new Error('Trip not found');
    }

    const trip = tripResult.rows[0];
    
    logExceptionFlow('VALIDATE_PROGRESSION', 'Checking trip progression', {
      trip_id: tripLogId,
      status: trip.status,
      is_exception: trip.is_exception,
      has_pending_approval: !!trip.approval_id
    });

    // If trip status is exception_pending, check approval status
    if (trip.status === EXCEPTION_STATES.PENDING) {
      // Trip cannot progress until approved
      if (!trip.approval_id) {
        return {
          canProgress: false,
          reason: 'No approval request found for exception',
          nextAction: 'create_approval_request'
        };
      }

      // Check latest approval status
      const latestApprovalResult = await client.query(`
        SELECT status, reviewed_at, reviewed_by, notes
        FROM approvals 
        WHERE trip_log_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [tripLogId]);

      if (latestApprovalResult.rows.length === 0) {
        return {
          canProgress: false,
          reason: 'Exception pending admin approval',
          nextAction: 'await_approval'
        };
      }

      const latestApproval = latestApprovalResult.rows[0];
      
      if (latestApproval.status === 'pending') {
        return {
          canProgress: false,
          reason: 'Exception pending admin approval',
          nextAction: 'await_approval',
          approval_data: latestApproval
        };
      } else if (latestApproval.status === 'rejected') {
        return {
          canProgress: false,
          reason: 'Exception was rejected - trip cancelled',
          nextAction: 'trip_cancelled',
          approval_data: latestApproval
        };
      } else if (latestApproval.status === 'approved') {
        // Exception was approved - trip can progress
        return {
          canProgress: true,
          reason: 'Exception approved - can proceed',
          nextAction: 'continue_trip',
          approval_data: latestApproval,
          requiresStatusUpdate: true
        };
      }
    }

    // For non-exception trips or already processed exceptions
    return {
      canProgress: true,
      reason: 'Normal trip flow',
      nextAction: 'continue_trip'
    };

  } catch (error) {
    logExceptionError('VALIDATE_PROGRESSION', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Create exception and set trip to pending state
 */
async function createExceptionAndSetPending(client, tripLogId, exceptionData) {
  try {
    await client.query('BEGIN');

    logExceptionFlow('CREATE_EXCEPTION', 'Creating exception request', {
      trip_id: tripLogId,
      exception_type: exceptionData.exception_type
    });

    // Check if exception already exists for this trip
    const existingException = await client.query(
      'SELECT id FROM approvals WHERE trip_log_id = $1 AND status = $2',
      [tripLogId, 'pending']
    );

    if (existingException.rows.length > 0) {
      await client.query('ROLLBACK');
      throw new Error('Pending exception already exists for this trip');
    }

    // Create approval request
    const insertQuery = `
      INSERT INTO approvals (
        trip_log_id, exception_type, exception_description, 
        severity, reported_by, status, requested_at,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const approvalResult = await client.query(insertQuery, [
      tripLogId,
      exceptionData.exception_type,
      exceptionData.exception_description,
      exceptionData.severity || 'medium',
      exceptionData.reported_by
    ]);

    // Update trip log to mark as exception and set status to exception_pending
    await client.query(`
      UPDATE trip_logs 
      SET is_exception = true, 
          exception_reason = $1, 
          status = 'exception_pending',
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [
      exceptionData.exception_description, 
      JSON.stringify({
        exception_created_at: new Date().toISOString(),
        original_status: exceptionData.original_status || 'loading_start',
        pending_assignment_id: exceptionData.pending_assignment_id
      }),
      tripLogId
    ]);

    await client.query('COMMIT');

    logExceptionFlow('CREATE_EXCEPTION', 'Exception created successfully', {
      trip_id: tripLogId,
      approval_id: approvalResult.rows[0].id
    });

    return {
      success: true,
      approval_id: approvalResult.rows[0].id,
      trip_status: EXCEPTION_STATES.PENDING
    };

  } catch (error) {
    await client.query('ROLLBACK');
    logExceptionError('CREATE_EXCEPTION', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Process admin approval and update trip status
 */
async function processApprovalAndUpdateTrip(client, approvalId, decision, reviewedBy, notes = '') {
  const startTime = Date.now();

  try {
    await client.query('BEGIN');

    logExceptionFlow('PROCESS_APPROVAL', 'Processing approval decision', {
      approval_id: approvalId,
      decision,
      reviewed_by: reviewedBy
    });

    // Get approval details with trip information
    const approvalResult = await client.query(`
      SELECT 
        a.id, a.status as approval_status, a.exception_type, a.exception_description,
        a.trip_log_id, a.severity, a.reported_by,
        tl.id as trip_id, tl.status as trip_status, tl.notes as trip_notes,
        tl.assignment_id, tl.is_exception, tl.exception_reason
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      WHERE a.id = $1
      FOR UPDATE
    `, [approvalId]);

    if (approvalResult.rows.length === 0) {
      await client.query('ROLLBACK');
      throw new Error('Approval request not found');
    }

    const approval = approvalResult.rows[0];

    if (approval.approval_status !== 'pending') {
      await client.query('ROLLBACK');
      throw new Error(`Approval request has already been processed. Current status: ${approval.approval_status}`);
    }

    // Update approval decision
    await client.query(`
      UPDATE approvals 
      SET status = $1, reviewed_by = $2, reviewed_at = CURRENT_TIMESTAMP, 
          notes = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
    `, [decision, reviewedBy, notes, approvalId]);

    let tripUpdateResult;

    if (decision === 'approved') {
      // Handle approved exception
      tripUpdateResult = await handleApprovedExceptionFlow(client, approval, reviewedBy);
    } else if (decision === 'rejected') {
      // Handle rejected exception
      tripUpdateResult = await handleRejectedExceptionFlow(client, approval, reviewedBy);
    }

    await client.query('COMMIT');

    const processingTime = Date.now() - startTime;

    logExceptionFlow('PROCESS_APPROVAL', 'Approval processed successfully', {
      approval_id: approvalId,
      decision,
      processing_time_ms: processingTime,
      new_trip_status: tripUpdateResult?.new_status
    });

    return {
      success: true,
      decision,
      processing_time: processingTime,
      trip_update: tripUpdateResult
    };

  } catch (error) {
    await client.query('ROLLBACK');
    const processingTime = Date.now() - startTime;
    logExceptionError('PROCESS_APPROVAL', error, {
      approval_id: approvalId,
      processing_time_ms: processingTime
    });
    throw error;
  }
}

/**
 * Handle approved exception flow - mark trip as completed per Trip Flow Logic
 * According to Trip Flow Logic: when truck returns to unassigned location (A→B→C),
 * it has already completed the full cycle, so trip should be marked as 'trip_completed'
 */
async function handleApprovedExceptionFlow(client, approval, reviewedBy) {
  try {
    const { trip_log_id, exception_description } = approval;

    logExceptionFlow('HANDLE_APPROVED', 'Processing approved exception', {
      trip_id: trip_log_id,
      exception_type: approval.exception_type
    });

    // Parse trip notes to get pending assignment information
    let tripNotes = {};
    try {
      if (approval.trip_notes && typeof approval.trip_notes === 'string') {
        tripNotes = JSON.parse(approval.trip_notes);
      } else if (approval.trip_notes && typeof approval.trip_notes === 'object') {
        tripNotes = approval.trip_notes;
      }
    } catch (e) {
      logExceptionError('PARSE_TRIP_NOTES', e, { trip_notes: approval.trip_notes });
      tripNotes = {};
    }

    // Check if this is a route deviation that requires assignment change
    // Support both old workflow (pending_assignment_id) and new workflow (proposed_assignment_data)
    const hasOldWorkflow = tripNotes.pending_assignment_id;
    const hasNewWorkflow = tripNotes.proposed_assignment_data && tripNotes.workflow_version === 'no_pending_assignment';

    logExceptionFlow('HANDLE_APPROVED', 'Route deviation condition check', {
      exception_description: exception_description,
      has_loading_text: exception_description && (exception_description.includes('Loading at') || exception_description.includes('loading at')),
      hasOldWorkflow: hasOldWorkflow,
      hasNewWorkflow: hasNewWorkflow,
      tripNotes: tripNotes
    });

    if (exception_description && (exception_description.includes('Loading at') || exception_description.includes('loading at')) && (hasOldWorkflow || hasNewWorkflow)) {
      
      let assignmentCheck = { rows: [] };
      let assignmentData = null;

      if (hasOldWorkflow) {
        // Old workflow: Check if pending assignment exists
        assignmentCheck = await client.query(
          'SELECT id, status, truck_id, loading_location_id, unloading_location_id FROM assignments WHERE id = $1',
          [tripNotes.pending_assignment_id]
        );

        logExceptionFlow('HANDLE_APPROVED', 'Old workflow - Assignment check result', {
          pending_assignment_id: tripNotes.pending_assignment_id,
          assignment_found: assignmentCheck.rows.length > 0,
          assignment_status: assignmentCheck.rows[0]?.status
        });

        if (assignmentCheck.rows.length > 0) {
          assignmentData = assignmentCheck.rows[0];
        }
      } else if (hasNewWorkflow) {
        // New workflow: Use proposed assignment data from trip notes
        assignmentData = {
          truck_id: tripNotes.proposed_assignment_data.truck_id,
          loading_location_id: tripNotes.proposed_assignment_data.loading_location_id,
          unloading_location_id: tripNotes.proposed_assignment_data.unloading_location_id
        };

        logExceptionFlow('HANDLE_APPROVED', 'New workflow - Using proposed assignment data', {
          proposed_assignment_data: tripNotes.proposed_assignment_data,
          workflow_version: tripNotes.workflow_version
        });

        // Simulate successful assignment check for new workflow
        assignmentCheck.rows = [assignmentData];
      }

      logExceptionFlow('HANDLE_APPROVED', 'Assignment check result', {
        assignmentCheck_rows_length: assignmentCheck.rows.length,
        assignmentData: assignmentData,
        hasOldWorkflow: hasOldWorkflow,
        hasNewWorkflow: hasNewWorkflow
      });

      if (assignmentCheck.rows.length === 0) {
        logExceptionFlow('HANDLE_APPROVED', 'Pending assignment not found, updating trip status only', {
          pending_assignment_id: tripNotes.pending_assignment_id
        });
        
        // Mark trip as completed per Trip Flow Logic
        // The truck has already performed Loading→Unloading→Return cycle
        await client.query(`
          UPDATE trip_logs
          SET status = 'trip_completed',
              trip_completed_time = CURRENT_TIMESTAMP,
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $3
          AND status = 'exception_pending'
        `, [
          reviewedBy,
          JSON.stringify({
            approval_processed_at: new Date().toISOString(),
            completion_reason: 'exception_approved_trip_completed',
            fallback_reason: 'pending_assignment_not_found'
          }),
          trip_log_id
        ]);

      } else {
        // NEW APPROACH: Create a brand new assignment with 'assigned' status FIRST
        // This eliminates the race condition and pending_approval status issues

        let assignmentData;
        let truckNumber;
        let newAssignmentId;

        if (hasOldWorkflow) {
          // Old workflow: Get data from existing pending assignment
          const pendingAssignment = assignmentCheck.rows[0];

          logExceptionFlow('HANDLE_APPROVED', 'Old workflow - Creating new assignment from pending assignment', {
            original_pending_assignment_id: tripNotes.pending_assignment_id,
            truck_id: pendingAssignment.truck_id,
            loading_location_id: pendingAssignment.loading_location_id,
            unloading_location_id: pendingAssignment.unloading_location_id
          });

          // Get additional assignment data needed for creation
          const fullAssignmentData = await client.query(`
            SELECT
                a.truck_id, a.driver_id, a.loading_location_id, a.unloading_location_id,
                a.priority, a.expected_loads_per_day, a.assigned_date,
                dt.truck_number
            FROM assignments a
            JOIN dump_trucks dt ON a.truck_id = dt.id
            WHERE a.id = $1
          `, [tripNotes.pending_assignment_id]);

          if (fullAssignmentData.rows.length === 0) {
            throw new Error(`Cannot retrieve full assignment data for ID ${tripNotes.pending_assignment_id}`);
          }

          assignmentData = fullAssignmentData.rows[0];
          truckNumber = assignmentData.truck_number;

        } else if (hasNewWorkflow) {
          // New workflow: Use proposed assignment data from trip notes
          const proposedData = tripNotes.proposed_assignment_data;

          logExceptionFlow('HANDLE_APPROVED', 'New workflow - Creating new assignment from proposed data', {
            proposed_assignment_data: proposedData,
            workflow_version: tripNotes.workflow_version
          });

          // Get truck number for the new assignment
          const truckData = await client.query(`
            SELECT truck_number FROM dump_trucks WHERE id = $1
          `, [proposedData.truck_id]);

          if (truckData.rows.length === 0) {
            throw new Error(`Cannot find truck with ID ${proposedData.truck_id}`);
          }

          truckNumber = truckData.rows[0].truck_number;

          // Use proposed assignment data directly
          assignmentData = {
            truck_id: proposedData.truck_id,
            driver_id: proposedData.driver_id,
            loading_location_id: proposedData.loading_location_id,
            unloading_location_id: proposedData.unloading_location_id,
            priority: proposedData.priority,
            expected_loads_per_day: proposedData.expected_loads_per_day,
            assigned_date: proposedData.assigned_date,
            truck_number: truckNumber
          };
        }

        // Generate new assignment code
        const timestamp = Date.now();
        const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
        const newAssignmentCode = `ASG-${timestamp}-${randomSuffix}`;

        // Check for existing assignment to prevent duplicate constraint violation
        const existingAssignmentCheck = await client.query(`
          SELECT id, assignment_code, status
          FROM assignments
          WHERE truck_id = $1
            AND loading_location_id = $2
            AND unloading_location_id = $3
            AND status IN ('assigned', 'in_progress')
          ORDER BY created_at DESC
          LIMIT 1
        `, [
          assignmentData.truck_id,
          assignmentData.loading_location_id,
          assignmentData.unloading_location_id
        ]);

        let newAssignment;
        if (existingAssignmentCheck.rows.length > 0) {
          // Use existing assignment instead of creating duplicate
          newAssignment = existingAssignmentCheck.rows[0];
          logExceptionFlow('HANDLE_APPROVED', 'Using existing assignment instead of creating duplicate', {
            existing_assignment_id: newAssignment.id,
            existing_assignment_code: newAssignment.assignment_code,
            status: newAssignment.status
          });
        } else {
          // Create new assignment with 'assigned' status (skip 'pending_approval' entirely)
          logExceptionFlow('HANDLE_APPROVED', 'About to create new assignment', {
            newAssignmentCode: newAssignmentCode,
            assignmentData: assignmentData
          });

          const newAssignmentResult = await client.query(`
            INSERT INTO assignments (
                assignment_code, truck_id, driver_id, loading_location_id, unloading_location_id,
                assigned_date, status, priority, expected_loads_per_day,
                created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id, assignment_code, status
          `, [
            newAssignmentCode,
            assignmentData.truck_id,
            assignmentData.driver_id,
            assignmentData.loading_location_id,
            assignmentData.unloading_location_id,
            assignmentData.assigned_date || new Date().toISOString().split('T')[0],
            'assigned', // Start directly as 'assigned', not 'pending_approval'
            assignmentData.priority || 'normal',
            assignmentData.expected_loads_per_day || 1
          ]);

          newAssignment = newAssignmentResult.rows[0];

          logExceptionFlow('HANDLE_APPROVED', 'New assignment created successfully', {
            new_assignment_id: newAssignment.id,
            new_assignment_code: newAssignment.assignment_code,
            new_assignment_status: newAssignment.status,
            truck_number: assignmentData.truck_number
          });
        }

        newAssignmentId = newAssignment.id;

        logExceptionFlow('HANDLE_APPROVED', 'Assignment ready for trip creation', {
          assignment_id: newAssignment.id,
          assignment_code: newAssignment.assignment_code,
          assignment_status: newAssignment.status,
          is_existing: existingAssignmentCheck.rows.length > 0
        });

        // NOW update trip to use the NEW assignment
        logExceptionFlow('HANDLE_APPROVED', 'Updating trip with new assignment', {
          trip_id: trip_log_id,
          new_assignment_id: newAssignmentId
        });

        // Update trip to use new assignment and mark as completed per Trip Flow Logic
        await client.query(`
          UPDATE trip_logs
          SET assignment_id = $1,
              status = 'trip_completed',
              trip_completed_time = CURRENT_TIMESTAMP,
              exception_approved_by = $2,
              exception_approved_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $4
          AND status = 'exception_pending'
        `, [
          newAssignmentId, // Use the NEW assignment ID
          reviewedBy,
          JSON.stringify({
            approval_processed_at: new Date().toISOString(),
            completion_reason: 'exception_approved_trip_completed',
            original_assignment_id: tripNotes.original_assignment_id || approval.assignment_id,
            revised_flow: tripNotes.revised_flow || 'Custom route approved',
            is_multiple_deviation: tripNotes.is_multiple_deviation,
            previous_deviation_ids: tripNotes.previous_deviation_ids || [],
            new_assignment_created: true,
            new_assignment_id: newAssignmentId,
            new_assignment_code: newAssignment.assignment_code,
            workflow_type: hasNewWorkflow ? 'new_workflow_no_pending_assignment' : 'old_workflow_pending_assignment'
          }),
          trip_log_id
        ]);
      }
    } else {
      // For other types of exceptions, mark trip as completed per Trip Flow Logic
      await client.query(`
        UPDATE trip_logs
        SET status = 'trip_completed',
            trip_completed_time = CURRENT_TIMESTAMP,
            exception_approved_by = $1,
            exception_approved_at = CURRENT_TIMESTAMP,
            notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
        AND status = 'exception_pending'
      `, [
        reviewedBy,
        JSON.stringify({
          approval_processed_at: new Date().toISOString(),
          completion_reason: 'exception_approved_trip_completed',
          exception_type: approval.exception_type
        }),
        trip_log_id
      ]);
    }

    return {
      new_status: TRIP_STATES.TRIP_COMPLETED,
      assignment_updated: !!tripNotes.pending_assignment_id,
      pending_assignment_id: tripNotes.pending_assignment_id
    };

  } catch (error) {
    logExceptionError('HANDLE_APPROVED', error, { trip_id: approval.trip_log_id });
    throw error;
  }
}

/**
 * Handle rejected exception flow - cancel trip
 */
async function handleRejectedExceptionFlow(client, approval, reviewedBy) {
  try {
    const { trip_log_id } = approval;

    logExceptionFlow('HANDLE_REJECTED', 'Processing rejected exception', {
      trip_id: trip_log_id
    });

    // Mark trip as cancelled due to rejected exception
    await client.query(`
      UPDATE trip_logs 
      SET status = 'cancelled',
          exception_approved_by = $1,
          exception_approved_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [
      reviewedBy, 
      JSON.stringify({ 
        cancellation_reason: 'Exception rejected',
        rejected_at: new Date().toISOString()
      }),
      trip_log_id
    ]);

    return {
      new_status: TRIP_STATES.CANCELLED,
      reason: 'Exception rejected by admin'
    };

  } catch (error) {
    logExceptionError('HANDLE_REJECTED', error, { trip_id: approval.trip_log_id });
    throw error;
  }
}

/**
 * Check approval status for a specific trip
 */
async function checkApprovalStatus(client, tripLogId) {
  try {
    const result = await client.query(`
      SELECT status, reviewed_at, reviewed_by, notes
      FROM approvals 
      WHERE trip_log_id = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [tripLogId]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0].status;
  } catch (error) {
    logExceptionError('CHECK_APPROVAL_STATUS', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Get comprehensive trip status including exception information
 */
async function getTripStatus(client, tripLogId) {
  try {
    const result = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.is_exception,
        tl.exception_reason,
        tl.exception_approved_at,
        tl.exception_approved_by,
        tl.assignment_id,
        tl.notes,
        a.id as approval_id,
        a.status as approval_status,
        a.exception_type,
        a.exception_description,
        a.severity,
        a.reviewed_at,
        a.reviewed_by,
        u.full_name as reviewed_by_name
      FROM trip_logs tl
      LEFT JOIN approvals a ON tl.id = a.trip_log_id
      LEFT JOIN users u ON a.reviewed_by = u.id
      WHERE tl.id = $1
      ORDER BY a.created_at DESC
    `, [tripLogId]);

    if (result.rows.length === 0) {
      throw new Error('Trip not found');
    }

    return result.rows[0];
  } catch (error) {
    logExceptionError('GET_TRIP_STATUS', error, { trip_id: tripLogId });
    throw error;
  }
}

/**
 * Unlock trip for normal progression after exception approval
 * Ensures all fields are set so trip can move through loading, traveling, unloading, completed
 */
async function unlockTripForNormalProgression(client, tripLogId) {
  // Remove any exception-blocking flags or fields if present
  await client.query(`
    UPDATE trip_logs
    SET is_exception = false,
        exception_reason = NULL,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = $1
      AND status = 'loading_start'
  `, [tripLogId]);
}

// Note: unlockTripForNormalProgression is no longer needed for exception approval
// since trips are now marked as 'trip_completed' per Trip Flow Logic requirements

module.exports = {
  EXCEPTION_STATES,
  TRIP_STATES,
  validateTripProgression,
  createExceptionAndSetPending,
  processApprovalAndUpdateTrip,
  handleApprovedExceptionFlow,
  handleRejectedExceptionFlow,
  checkApprovalStatus,
  getTripStatus,
  logExceptionFlow,
  logExceptionError,
  unlockTripForNormalProgression // export for use in other modules if needed
};
