#!/usr/bin/env node

const { getClient } = require('./server/config/database');

async function checkLocationData() {
  const client = await getClient();
  
  try {
    // Get all locations
    const result = await client.query('SELECT id, location_code, name, type FROM locations ORDER BY id');
    console.log('All locations:');
    result.rows.forEach(loc => {
      console.log(`  ID: ${loc.id}, Code: ${loc.location_code}, Name: ${loc.name}, Type: ${loc.type}`);
    });
    
    // Check POINT C specifically
    const pointC = await client.query("SELECT * FROM locations WHERE name = 'POINT C - LOADING'");
    if (pointC.rows.length > 0) {
      console.log('\nPOINT C details:');
      console.log(pointC.rows[0]);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    client.release();
  }
}

checkLocationData();
