#!/usr/bin/env node

/**
 * Scanner Simulation Test: Reproduce the Route Deviation Exception Issue
 * 
 * This script simulates the exact scanner workflow to reproduce the issue
 * where DT-100 scanning at Location A triggers a false route deviation exception.
 */

const { getClient } = require('./server/config/database');

// Import the scanner logic functions
const { assignmentValidator } = require('./server/utils/AssignmentValidator');

async function simulateScannerWorkflow() {
  console.log('🔬 Simulating Scanner Workflow for DT-100 at Location A...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Simulate the exact scanner workflow
    console.log('📱 Step 1: Simulating QR Code Scan');
    console.log('=' .repeat(50));
    
    const truckNumber = 'DT-100';
    const locationId = 1; // Point A - Main Loading Site
    const userId = 1; // Admin user
    
    console.log(`Truck: ${truckNumber}`);
    console.log(`Location ID: ${locationId}`);
    console.log(`User ID: ${userId}`);
    
    // Step 2: Get truck information (like scanner does)
    console.log('\n🚛 Step 2: Getting Truck Information');
    console.log('=' .repeat(50));
    
    const truckResult = await client.query(`
      SELECT id, truck_number, license_plate, status
      FROM dump_trucks
      WHERE truck_number = $1
    `, [truckNumber]);
    
    if (truckResult.rows.length === 0) {
      console.log('❌ Truck not found!');
      return;
    }
    
    const truck = truckResult.rows[0];
    console.log(`✅ Truck found: ID ${truck.id}, Status: ${truck.status}`);
    
    // Step 3: Get location information
    console.log('\n📍 Step 3: Getting Location Information');
    console.log('=' .repeat(50));
    
    const locationResult = await client.query(`
      SELECT id, location_code, name, type, status
      FROM locations
      WHERE id = $1
    `, [locationId]);
    
    if (locationResult.rows.length === 0) {
      console.log('❌ Location not found!');
      return;
    }
    
    const location = locationResult.rows[0];
    console.log(`✅ Location found: ${location.name} (${location.location_code}), Type: ${location.type}, Status: ${location.status}`);
    
    // Step 4: Simulate getCurrentTripAndAssignment function
    console.log('\n🔄 Step 4: Checking Current Trip and Assignment');
    console.log('=' .repeat(50));
    
    const currentTripResult = await client.query(`
      WITH current_trip AS (
        SELECT tl.*, tl.assignment_id as trip_assignment_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1
          AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
        ORDER BY tl.created_at DESC
        LIMIT 1
      ),
      current_assignment AS (
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          COALESCE(ct.trip_assignment_id, a.id) as active_assignment_id
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN current_trip ct ON ct.trip_assignment_id = a.id
        WHERE a.truck_id = $1
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
        LIMIT 1
      )
      SELECT
        ct.id as trip_id,
        ct.trip_assignment_id,
        ct.trip_number,
        ct.status as trip_status,
        ct.loading_start_time,
        ct.loading_end_time,
        ct.unloading_start_time,
        ct.unloading_end_time,
        ct.is_exception,
        ct.exception_reason,
        ct.actual_loading_location_id,
        ct.actual_unloading_location_id,
        ca.assignment_id,
        ca.truck_id,
        ca.driver_id,
        ca.assignment_status,
        ca.priority,
        ca.expected_loads_per_day,
        ca.loading_location_id,
        ca.loading_location_name,
        ca.unloading_location_id,
        ca.unloading_location_name,
        ca.active_assignment_id
      FROM current_assignment ca
      LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.active_assignment_id
    `, [truck.id]);
    
    const tripData = currentTripResult.rows.length > 0 ? currentTripResult.rows[0] : null;
    
    if (tripData && tripData.trip_id) {
      console.log(`🔄 Active trip found: Trip ${tripData.trip_id} (${tripData.trip_status})`);
      console.log(`   Assignment: ${tripData.assignment_id} (${tripData.loading_location_name} → ${tripData.unloading_location_name})`);
    } else if (tripData && tripData.assignment_id) {
      console.log(`📋 Assignment found but no active trip: Assignment ${tripData.assignment_id}`);
      console.log(`   Route: ${tripData.loading_location_name} → ${tripData.unloading_location_name}`);
    } else {
      console.log(`❌ No current trip or assignment found`);
    }
    
    // Step 5: Test assignment validation logic
    console.log('\n🎯 Step 5: Testing Assignment Validation Logic');
    console.log('=' .repeat(50));
    
    if (!tripData || !tripData.trip_id) {
      console.log('No active trip - checking for valid assignments...');
      
      // This is the critical path - check hasValidAssignmentForLocation
      console.log('\n🔍 Testing hasValidAssignmentForLocation...');
      
      try {
        const assignmentCheck = await assignmentValidator.hasValidAssignmentForLocation({
          truckNumber: truckNumber,
          locationId: locationId,
          client: client
        });
        
        console.log(`Result: hasValidAssignment = ${assignmentCheck.hasValidAssignment}`);
        console.log(`Message: ${assignmentCheck.message}`);
        console.log(`Assignments found: ${assignmentCheck.assignments?.length || 0}`);
        
        if (assignmentCheck.assignments && assignmentCheck.assignments.length > 0) {
          assignmentCheck.assignments.forEach((assignment, index) => {
            console.log(`  ${index + 1}. Assignment ${assignment.id}: ${assignment.loading_location} → ${assignment.unloading_location}`);
            console.log(`     Location role: ${assignment.location_role}`);
          });
        }
        
        if (assignmentCheck.hasValidAssignment) {
          console.log('✅ EXPECTED: Should create new trip without exception');
        } else {
          console.log('❌ PROBLEM: Would trigger route deviation exception');
        }
        
      } catch (validationError) {
        console.log(`❌ Assignment validation failed: ${validationError.message}`);
      }
      
      // Step 6: Check the fallback logic
      console.log('\n🔄 Step 6: Testing Fallback Assignment Logic');
      console.log('=' .repeat(50));
      
      const allAssignmentsResult = await client.query(`
        SELECT 
          a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
          a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
          dt.truck_number, dt.status as truck_status,
          ll.name as loading_location, ul.name as unloading_location,
          d.full_name as driver_name
        FROM assignments a
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        WHERE dt.truck_number = $1
          AND a.status IN ('assigned', 'in_progress')
        ORDER BY a.created_at DESC
      `, [truckNumber]);
      
      console.log(`Found ${allAssignmentsResult.rows.length} assignments for ${truckNumber}`);
      
      if (allAssignmentsResult.rows.length > 0) {
        const assignmentForLocation = allAssignmentsResult.rows.find(assignment =>
          assignment.loading_location_id === locationId || assignment.unloading_location_id === locationId
        );
        
        if (assignmentForLocation) {
          console.log(`✅ FOUND assignment for location: ${assignmentForLocation.assignment_code}`);
          console.log(`   Route: ${assignmentForLocation.loading_location} → ${assignmentForLocation.unloading_location}`);
          console.log('✅ EXPECTED: Should create new trip without exception');
        } else {
          console.log(`❌ NO assignment found for location ID ${locationId}`);
          console.log('❌ PROBLEM: Would trigger route deviation exception');
          
          const mostRecentAssignment = allAssignmentsResult.rows[0];
          console.log(`   Most recent assignment: ${mostRecentAssignment.assignment_code}`);
          console.log(`   Expected location: ${mostRecentAssignment.loading_location} (ID: ${mostRecentAssignment.loading_location_id})`);
          console.log(`   Actual location: ${location.name} (ID: ${locationId})`);
        }
      } else {
        console.log('❌ NO assignments found at all - would trigger unassigned trip exception');
      }
    } else {
      console.log('Active trip exists - would use existing trip logic');
    }
    
  } catch (error) {
    console.error('❌ Simulation failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the simulation
if (require.main === module) {
  simulateScannerWorkflow()
    .then(() => {
      console.log('\n✅ Simulation completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Simulation failed:', error);
      process.exit(1);
    });
}

module.exports = { simulateScannerWorkflow };
