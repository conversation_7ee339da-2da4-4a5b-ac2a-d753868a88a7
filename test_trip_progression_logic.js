#!/usr/bin/env node

/**
 * Test Trip Progression Logic: Simulate the exact scanner workflow
 * to understand why trips get stuck in loading_start
 */

const { getClient } = require('./server/config/database');

async function testTripProgressionLogic() {
  console.log('🧪 Testing Trip Progression Logic...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Check current stuck trips
    console.log('🔍 Step 1: Analyzing Current Stuck Trips');
    console.log('=' .repeat(60));
    
    const stuckTripsResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.loading_start_time,
        tl.assignment_id,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location,
        ll.type as loading_location_type,
        ul.name as unloading_location,
        ul.type as unloading_location_type
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status = 'loading_start'
      ORDER BY tl.created_at DESC
    `);
    
    if (stuckTripsResult.rows.length > 0) {
      console.log(`Found ${stuckTripsResult.rows.length} stuck trips:`);
      stuckTripsResult.rows.forEach(trip => {
        console.log(`\n  🚫 Trip ${trip.id}:`);
        console.log(`     Status: ${trip.status}`);
        console.log(`     Assignment: ${trip.assignment_id}`);
        console.log(`     Loading location: ${trip.loading_location} (ID: ${trip.loading_location_id}, Type: ${trip.loading_location_type})`);
        console.log(`     Unloading location: ${trip.unloading_location} (ID: ${trip.unloading_location_id}, Type: ${trip.unloading_location_type})`);
        console.log(`     Started: ${trip.loading_start_time}`);
      });
      
      // Step 2: Test the progression logic for the first stuck trip
      const testTrip = stuckTripsResult.rows[0];
      console.log(`\n🎯 Step 2: Testing Progression Logic for Trip ${testTrip.id}`);
      console.log('=' .repeat(60));
      
      // Simulate what should happen when scanning at the loading location again
      console.log(`Simulating scan at loading location: ${testTrip.loading_location} (Type: ${testTrip.loading_location_type})`);
      
      // Test the FIXED getCurrentTripAndAssignment logic
      console.log('\n🔄 Testing FIXED getCurrentTripAndAssignment...');
      const currentTripResult = await client.query(`
        WITH current_trip AS (
          SELECT tl.*, tl.assignment_id as trip_assignment_id
          FROM trip_logs tl
          JOIN assignments a ON tl.assignment_id = a.id
          WHERE a.truck_id = 1
            AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
          ORDER BY tl.created_at DESC
          LIMIT 1
        ),
        trip_assignment AS (
          -- Get the assignment for the active trip (if any)
          SELECT
            a.id as assignment_id,
            a.truck_id,
            a.driver_id,
            a.status as assignment_status,
            a.priority,
            a.expected_loads_per_day,
            a.assigned_date,
            a.start_time,
            a.end_time,
            a.created_at as assignment_created_at,
            ll.id as loading_location_id,
            ll.name as loading_location_name,
            ul.id as unloading_location_id,
            ul.name as unloading_location_name,
            ct.trip_assignment_id as active_assignment_id
          FROM current_trip ct
          JOIN assignments a ON ct.trip_assignment_id = a.id
          JOIN locations ll ON a.loading_location_id = ll.id
          JOIN locations ul ON a.unloading_location_id = ul.id
        ),
        fallback_assignment AS (
          -- Fallback: get most recent assignment if no active trip
          SELECT
            a.id as assignment_id,
            a.truck_id,
            a.driver_id,
            a.status as assignment_status,
            a.priority,
            a.expected_loads_per_day,
            a.assigned_date,
            a.start_time,
            a.end_time,
            a.created_at as assignment_created_at,
            ll.id as loading_location_id,
            ll.name as loading_location_name,
            ul.id as unloading_location_id,
            ul.name as unloading_location_name,
            a.id as active_assignment_id
          FROM assignments a
          JOIN locations ll ON a.loading_location_id = ll.id
          JOIN locations ul ON a.unloading_location_id = ul.id
          WHERE a.truck_id = 1
            AND a.status IN ('assigned', 'in_progress')
            AND NOT EXISTS (SELECT 1 FROM current_trip)
          ORDER BY a.created_at DESC
          LIMIT 1
        ),
        combined_assignment AS (
          SELECT * FROM trip_assignment
          UNION ALL
          SELECT * FROM fallback_assignment
          LIMIT 1
        )
        SELECT
          ct.id as trip_id,
          ct.trip_assignment_id,
          ct.trip_number,
          ct.status as trip_status,
          ct.loading_start_time,
          ct.loading_end_time,
          ct.unloading_start_time,
          ct.unloading_end_time,
          ct.is_exception,
          ct.exception_reason,
          ct.actual_loading_location_id,
          ct.actual_unloading_location_id,
          ca.assignment_id,
          ca.truck_id,
          ca.driver_id,
          ca.assignment_status,
          ca.priority,
          ca.expected_loads_per_day,
          ca.loading_location_id,
          ca.loading_location_name,
          ca.unloading_location_id,
          ca.unloading_location_name,
          ca.active_assignment_id
        FROM combined_assignment ca
        LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.assignment_id
      `);
      
      if (currentTripResult.rows.length > 0) {
        const tripData = currentTripResult.rows[0];
        console.log(`✅ Found current trip: ${tripData.trip_id} (${tripData.trip_status})`);
        console.log(`   Assignment: ${tripData.assignment_id}`);
        console.log(`   Loading location: ${tripData.loading_location_name} (ID: ${tripData.loading_location_id})`);
        
        // Test what determineNextAction would do
        console.log('\n🎯 Testing determineNextAction logic...');
        
        if (tripData.trip_status === 'loading_start') {
          console.log('✅ Trip is in loading_start status');
          console.log(`✅ Loading location type should be 'loading': ${testTrip.loading_location_type}`);
          
          if (testTrip.loading_location_type === 'loading') {
            console.log('✅ Location type is correct for loading operation');
            console.log('✅ Should call handleLoadingStart and update to loading_end');
            
            // Test if we can manually update the trip
            console.log('\n🔧 Testing manual trip progression...');
            const now = new Date();
            const loadingDuration = Math.round(
              (now - new Date(tripData.loading_start_time)) / (1000 * 60)
            );
            
            console.log(`Loading duration: ${loadingDuration} minutes`);
            console.log('Attempting to update trip to loading_end...');
            
            const updateResult = await client.query(`
              UPDATE trip_logs 
              SET status = $1, loading_end_time = $2, loading_duration_minutes = $3, updated_at = $4
              WHERE id = $5
              RETURNING *
            `, ['loading_end', now, loadingDuration, now, tripData.trip_id]);
            
            if (updateResult.rows.length > 0) {
              console.log('✅ Successfully updated trip to loading_end!');
              console.log(`   Trip ${tripData.trip_id} is now in loading_end status`);
              console.log(`   Loading duration: ${loadingDuration} minutes`);
            } else {
              console.log('❌ Failed to update trip status');
            }
          } else {
            console.log(`❌ Location type is '${testTrip.loading_location_type}', not 'loading'`);
          }
        } else {
          console.log(`❌ Trip status is '${tripData.trip_status}', not 'loading_start'`);
        }
      } else {
        console.log('❌ No current trip found by getCurrentTripAndAssignment');
      }
    } else {
      console.log('✅ No trips currently stuck in loading_start');
    }
    
    // Step 3: Check if there are any trips in other statuses
    console.log('\n📊 Step 3: Current Trip Status Distribution');
    console.log('=' .repeat(60));
    
    const statusResult = await client.query(`
      SELECT 
        tl.status,
        COUNT(*) as count,
        MAX(tl.created_at) as latest_created
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1
      GROUP BY tl.status
      ORDER BY latest_created DESC
    `);
    
    statusResult.rows.forEach(row => {
      console.log(`  ${row.status}: ${row.count} trips (latest: ${row.latest_created})`);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testTripProgressionLogic()
    .then(() => {
      console.log('\n✅ Trip progression logic test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Trip progression logic test failed:', error);
      process.exit(1);
    });
}

module.exports = { testTripProgressionLogic };
