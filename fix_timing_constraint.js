#!/usr/bin/env node

/**
 * Fix Timing Constraint Issue
 * 
 * This script fixes the "chk_trip_timing_sequence" constraint violations
 * by ensuring all trip timestamps follow the proper sequence.
 */

const { getClient } = require('./server/config/database');

async function fixTimingConstraint() {
  console.log('🔧 Fixing Trip Timing Constraint Issues...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Identify trips with timing constraint violations
    console.log('🔍 Step 1: Identifying Timing Constraint Violations');
    console.log('=' .repeat(60));
    
    const violationsResult = await client.query(`
      SELECT 
        id,
        status,
        loading_start_time,
        loading_end_time,
        unloading_start_time,
        unloading_end_time,
        trip_completed_time,
        CASE 
          WHEN loading_start_time IS NOT NULL AND loading_end_time IS NOT NULL 
               AND loading_end_time < loading_start_time THEN 'loading_end < loading_start'
          WHEN loading_end_time IS NOT NULL AND unloading_start_time IS NOT NULL 
               AND unloading_start_time < loading_end_time THEN 'unloading_start < loading_end'
          WHEN unloading_start_time IS NOT NULL AND unloading_end_time IS NOT NULL 
               AND unloading_end_time < unloading_start_time THEN 'unloading_end < unloading_start'
          WHEN unloading_end_time IS NOT NULL AND trip_completed_time IS NOT NULL 
               AND trip_completed_time < unloading_end_time THEN 'trip_completed < unloading_end'
          ELSE 'unknown'
        END as violation_type
      FROM trip_logs
      WHERE 
        (loading_start_time IS NOT NULL AND loading_end_time IS NOT NULL AND loading_end_time < loading_start_time)
        OR (loading_end_time IS NOT NULL AND unloading_start_time IS NOT NULL AND unloading_start_time < loading_end_time)
        OR (unloading_start_time IS NOT NULL AND unloading_end_time IS NOT NULL AND unloading_end_time < unloading_start_time)
        OR (unloading_end_time IS NOT NULL AND trip_completed_time IS NOT NULL AND trip_completed_time < unloading_end_time)
    `);
    
    if (violationsResult.rows.length > 0) {
      console.log(`Found ${violationsResult.rows.length} trips with timing violations:`);
      violationsResult.rows.forEach(trip => {
        console.log(`\n  🚫 Trip ${trip.id} (${trip.status}): ${trip.violation_type}`);
        console.log(`     Loading: ${trip.loading_start_time} → ${trip.loading_end_time}`);
        console.log(`     Unloading: ${trip.unloading_start_time} → ${trip.unloading_end_time}`);
        console.log(`     Completed: ${trip.trip_completed_time}`);
      });
    } else {
      console.log('✅ No timing constraint violations found');
    }
    
    // Step 2: Fix trips with proper timestamp sequences
    console.log('\n🔧 Step 2: Fixing Timestamp Sequences');
    console.log('=' .repeat(60));
    
    // Fix trips that need proper timestamp progression
    const tripsToFix = await client.query(`
      SELECT id, status, loading_start_time, created_at
      FROM trip_logs
      WHERE status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end')
        AND (
          loading_end_time IS NULL 
          OR unloading_start_time IS NULL 
          OR unloading_end_time IS NULL
          OR trip_completed_time IS NULL
        )
      ORDER BY created_at DESC
    `);
    
    console.log(`Found ${tripsToFix.rows.length} trips that need timestamp fixes`);
    
    for (const trip of tripsToFix.rows) {
      console.log(`\n🔄 Fixing Trip ${trip.id} (${trip.status})`);
      
      const baseTime = new Date(trip.loading_start_time || trip.created_at);
      const loadingEndTime = new Date(baseTime.getTime() + 5 * 60 * 1000); // +5 minutes
      const unloadingStartTime = new Date(loadingEndTime.getTime() + 10 * 60 * 1000); // +10 minutes
      const unloadingEndTime = new Date(unloadingStartTime.getTime() + 3 * 60 * 1000); // +3 minutes
      const tripCompletedTime = new Date(unloadingEndTime.getTime() + 8 * 60 * 1000); // +8 minutes
      
      try {
        // Update with proper timestamp sequence based on current status
        let updateQuery = '';
        let params = [];
        
        switch (trip.status) {
          case 'loading_start':
            updateQuery = `
              UPDATE trip_logs 
              SET loading_start_time = $1, updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `;
            params = [baseTime, trip.id];
            break;
            
          case 'loading_end':
            updateQuery = `
              UPDATE trip_logs 
              SET loading_start_time = $1, loading_end_time = $2, 
                  loading_duration_minutes = 5, updated_at = CURRENT_TIMESTAMP
              WHERE id = $3
            `;
            params = [baseTime, loadingEndTime, trip.id];
            break;
            
          case 'unloading_start':
            updateQuery = `
              UPDATE trip_logs 
              SET loading_start_time = $1, loading_end_time = $2, 
                  unloading_start_time = $3, loading_duration_minutes = 5,
                  travel_duration_minutes = 10, updated_at = CURRENT_TIMESTAMP
              WHERE id = $4
            `;
            params = [baseTime, loadingEndTime, unloadingStartTime, trip.id];
            break;
            
          case 'unloading_end':
            updateQuery = `
              UPDATE trip_logs 
              SET loading_start_time = $1, loading_end_time = $2, 
                  unloading_start_time = $3, unloading_end_time = $4,
                  loading_duration_minutes = 5, travel_duration_minutes = 10,
                  unloading_duration_minutes = 3, updated_at = CURRENT_TIMESTAMP
              WHERE id = $5
            `;
            params = [baseTime, loadingEndTime, unloadingStartTime, unloadingEndTime, trip.id];
            break;
        }
        
        if (updateQuery) {
          await client.query(updateQuery, params);
          console.log(`   ✅ Updated Trip ${trip.id} with proper timestamp sequence`);
        }
        
      } catch (error) {
        console.log(`   ❌ Failed to fix Trip ${trip.id}: ${error.message}`);
      }
    }
    
    // Step 3: Verify fixes
    console.log('\n✅ Step 3: Verifying Fixes');
    console.log('=' .repeat(60));
    
    const verificationResult = await client.query(`
      SELECT 
        COUNT(*) as total_trips,
        COUNT(CASE WHEN status = 'loading_start' THEN 1 END) as loading_start,
        COUNT(CASE WHEN status = 'loading_end' THEN 1 END) as loading_end,
        COUNT(CASE WHEN status = 'unloading_start' THEN 1 END) as unloading_start,
        COUNT(CASE WHEN status = 'unloading_end' THEN 1 END) as unloading_end,
        COUNT(CASE WHEN status = 'trip_completed' THEN 1 END) as trip_completed
      FROM trip_logs
      WHERE id IN (
        SELECT tl.id FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = 1
      )
    `);
    
    const stats = verificationResult.rows[0];
    console.log('Trip Status Distribution for DT-100:');
    console.log(`  loading_start: ${stats.loading_start}`);
    console.log(`  loading_end: ${stats.loading_end}`);
    console.log(`  unloading_start: ${stats.unloading_start}`);
    console.log(`  unloading_end: ${stats.unloading_end}`);
    console.log(`  trip_completed: ${stats.trip_completed}`);
    console.log(`  Total trips: ${stats.total_trips}`);
    
    // Check for remaining violations
    const remainingViolations = await client.query(`
      SELECT COUNT(*) as violation_count
      FROM trip_logs
      WHERE 
        (loading_start_time IS NOT NULL AND loading_end_time IS NOT NULL AND loading_end_time < loading_start_time)
        OR (loading_end_time IS NOT NULL AND unloading_start_time IS NOT NULL AND unloading_start_time < loading_end_time)
        OR (unloading_start_time IS NOT NULL AND unloading_end_time IS NOT NULL AND unloading_end_time < unloading_start_time)
        OR (unloading_end_time IS NOT NULL AND trip_completed_time IS NOT NULL AND trip_completed_time < unloading_end_time)
    `);
    
    const violationCount = remainingViolations.rows[0].violation_count;
    
    if (violationCount === '0') {
      console.log('\n🎉 SUCCESS: All timing constraint violations fixed!');
      console.log('✅ All trip timestamps now follow proper sequence');
      console.log('✅ Database constraint "chk_trip_timing_sequence" satisfied');
    } else {
      console.log(`\n⚠️  WARNING: ${violationCount} timing violations still exist`);
    }
    
  } catch (error) {
    console.error('❌ Fix failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the fix
if (require.main === module) {
  fixTimingConstraint()
    .then(() => {
      console.log('\n✅ Timing constraint fix completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Timing constraint fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixTimingConstraint };
