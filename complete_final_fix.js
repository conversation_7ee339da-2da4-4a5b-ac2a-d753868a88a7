#!/usr/bin/env node

/**
 * Final Fix Script: Complete remaining trip and test the system
 */

const { getClient } = require('./server/config/database');

async function completeFinalFix() {
  console.log('🔧 Completing final fix for assignment validation...\n');
  
  const client = await getClient();
  
  try {
    // Complete the remaining trip
    console.log('🔄 Completing Trip 31...');
    const result = await client.query(`
      UPDATE trip_logs 
      SET status = 'trip_completed',
          trip_completed_time = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = 31 AND status = 'unloading_end'
      RETURNING id, status
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ Trip 31 completed successfully');
    } else {
      console.log('ℹ️  Trip 31 was already completed');
    }
    
    // Final validation test
    console.log('\n🎯 Final Validation Test...');
    
    // Check for any remaining incomplete trips
    const incompleteTrips = await client.query(`
      SELECT tl.id, tl.status, a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
    `);
    
    console.log(`Incomplete trips: ${incompleteTrips.rows.length}`);
    
    // Check for pending exceptions
    const pendingExceptions = await client.query(`
      SELECT COUNT(*) as count
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 AND ap.status = 'pending'
    `);
    
    console.log(`Pending exceptions: ${pendingExceptions.rows[0].count}`);
    
    // Check valid assignments for Point A
    const validAssignments = await client.query(`
      SELECT COUNT(*) as count
      FROM assignments a
      WHERE a.truck_id = 1 
        AND a.status IN ('assigned', 'in_progress')
        AND a.loading_location_id = 1
    `);
    
    console.log(`Valid assignments for Point A: ${validAssignments.rows[0].count}`);
    
    const allGood = incompleteTrips.rows.length === 0 && 
                   pendingExceptions.rows[0].count === '0' && 
                   validAssignments.rows[0].count > 0;
    
    if (allGood) {
      console.log('\n🎉 SUCCESS! All issues resolved:');
      console.log('  ✅ No incomplete trips blocking assignment validation');
      console.log('  ✅ No pending exceptions');
      console.log('  ✅ Valid assignment exists for Point A');
      console.log('\n🚀 DT-100 can now scan at Point A without triggering false exceptions!');
    } else {
      console.log('\n⚠️  Some issues may still exist - check the counts above');
    }
    
  } catch (error) {
    console.error('❌ Final fix failed:', error.message);
  } finally {
    client.release();
  }
}

// Run the fix
if (require.main === module) {
  completeFinalFix()
    .then(() => {
      console.log('\n✅ Final fix completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Final fix failed:', error);
      process.exit(1);
    });
}

module.exports = { completeFinalFix };
