#!/usr/bin/env node

/**
 * Trip Progression Investigation: Analyze trip status progression issues
 * 
 * This script investigates why trips are stuck in loading_start status
 * and analyzes the trip progression logic.
 */

const { getClient } = require('./server/config/database');

async function investigateTripProgression() {
  console.log('🔍 Investigating Trip Status Progression Issues...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Analyze current trip statuses
    console.log('📊 Step 1: Current Trip Status Distribution');
    console.log('=' .repeat(60));
    
    const statusDistributionResult = await client.query(`
      SELECT 
        tl.status,
        COUNT(*) as count,
        COUNT(CASE WHEN a.truck_id = 1 THEN 1 END) as dt100_count
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      GROUP BY tl.status
      ORDER BY count DESC
    `);
    
    console.log('Trip Status Distribution:');
    statusDistributionResult.rows.forEach(row => {
      console.log(`  ${row.status}: ${row.count} total (${row.dt100_count} for DT-100)`);
    });
    
    // Step 2: Analyze stuck trips
    console.log('\n🚫 Step 2: Trips Stuck in loading_start');
    console.log('=' .repeat(60));
    
    const stuckTripsResult = await client.query(`
      SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.trip_completed_time,
        tl.created_at,
        a.assignment_code,
        dt.truck_number,
        ll.name as loading_location,
        ul.name as unloading_location,
        EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - tl.loading_start_time))/60 as minutes_stuck
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status = 'loading_start'
      ORDER BY tl.created_at DESC
    `);
    
    if (stuckTripsResult.rows.length > 0) {
      console.log(`Found ${stuckTripsResult.rows.length} trips stuck in loading_start:`);
      stuckTripsResult.rows.forEach(trip => {
        console.log(`\n  🚫 Trip ${trip.id} (${trip.truck_number})`);
        console.log(`     Assignment: ${trip.assignment_code}`);
        console.log(`     Route: ${trip.loading_location} → ${trip.unloading_location}`);
        console.log(`     Loading started: ${trip.loading_start_time}`);
        console.log(`     Stuck for: ${Math.round(trip.minutes_stuck)} minutes`);
        console.log(`     Loading end time: ${trip.loading_end_time || 'NULL'}`);
      });
    } else {
      console.log('✅ No trips currently stuck in loading_start');
    }
    
    // Step 3: Analyze complete trip progression patterns
    console.log('\n📈 Step 3: Trip Progression Patterns');
    console.log('=' .repeat(60));
    
    const progressionResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.trip_completed_time,
        dt.truck_number,
        a.assignment_code,
        CASE 
          WHEN tl.loading_start_time IS NOT NULL AND tl.loading_end_time IS NULL THEN 'Stuck at loading_start'
          WHEN tl.loading_end_time IS NOT NULL AND tl.unloading_start_time IS NULL THEN 'Stuck at loading_end'
          WHEN tl.unloading_start_time IS NOT NULL AND tl.unloading_end_time IS NULL THEN 'Stuck at unloading_start'
          WHEN tl.unloading_end_time IS NOT NULL AND tl.trip_completed_time IS NULL THEN 'Stuck at unloading_end'
          WHEN tl.trip_completed_time IS NOT NULL THEN 'Completed'
          ELSE 'Unknown'
        END as progression_status
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE dt.truck_number = 'DT-100'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `);
    
    console.log('Recent DT-100 trip progression patterns:');
    progressionResult.rows.forEach((trip, index) => {
      console.log(`\n  ${index + 1}. Trip ${trip.id} (${trip.assignment_code})`);
      console.log(`     Status: ${trip.status}`);
      console.log(`     Progression: ${trip.progression_status}`);
      console.log(`     Loading: ${trip.loading_start_time || 'NULL'} → ${trip.loading_end_time || 'NULL'}`);
      console.log(`     Unloading: ${trip.unloading_start_time || 'NULL'} → ${trip.unloading_end_time || 'NULL'}`);
      console.log(`     Completed: ${trip.trip_completed_time || 'NULL'}`);
    });
    
    // Step 4: Analyze successful trip completions
    console.log('\n✅ Step 4: Successful Trip Completions');
    console.log('=' .repeat(60));
    
    const successfulTripsResult = await client.query(`
      SELECT 
        tl.id,
        tl.trip_number,
        dt.truck_number,
        a.assignment_code,
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.trip_completed_time,
        EXTRACT(EPOCH FROM (tl.loading_end_time - tl.loading_start_time))/60 as loading_duration_minutes,
        EXTRACT(EPOCH FROM (tl.unloading_end_time - tl.unloading_start_time))/60 as unloading_duration_minutes,
        EXTRACT(EPOCH FROM (tl.trip_completed_time - tl.loading_start_time))/60 as total_duration_minutes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      WHERE tl.status = 'trip_completed'
        AND tl.loading_start_time IS NOT NULL
        AND tl.loading_end_time IS NOT NULL
        AND tl.unloading_start_time IS NOT NULL
        AND tl.unloading_end_time IS NOT NULL
        AND tl.trip_completed_time IS NOT NULL
      ORDER BY tl.created_at DESC
      LIMIT 5
    `);
    
    if (successfulTripsResult.rows.length > 0) {
      console.log(`Found ${successfulTripsResult.rows.length} successfully completed trips:`);
      successfulTripsResult.rows.forEach(trip => {
        console.log(`\n  ✅ Trip ${trip.id} (${trip.truck_number})`);
        console.log(`     Assignment: ${trip.assignment_code}`);
        console.log(`     Loading duration: ${Math.round(trip.loading_duration_minutes)} minutes`);
        console.log(`     Unloading duration: ${Math.round(trip.unloading_duration_minutes)} minutes`);
        console.log(`     Total duration: ${Math.round(trip.total_duration_minutes)} minutes`);
      });
    } else {
      console.log('❌ No successfully completed trips found with full progression');
    }
    
    // Step 5: Check scan logs for progression clues
    console.log('\n📱 Step 5: Recent Scan Logs Analysis');
    console.log('=' .repeat(60));
    
    const scanLogsResult = await client.query(`
      SELECT 
        sl.id,
        sl.scan_type,
        sl.scan_timestamp,
        sl.scanned_data,
        sl.is_valid,
        sl.validation_error,
        tl.id as trip_id,
        tl.status as trip_status,
        dt.truck_number,
        l.name as location_name
      FROM scan_logs sl
      LEFT JOIN trip_logs tl ON sl.trip_log_id = tl.id
      LEFT JOIN dump_trucks dt ON sl.scanned_truck_id = dt.id
      LEFT JOIN locations l ON sl.scanned_location_id = l.id
      WHERE sl.scan_timestamp >= CURRENT_TIMESTAMP - INTERVAL '1 day'
      ORDER BY sl.scan_timestamp DESC
      LIMIT 10
    `);
    
    console.log('Recent scan activity:');
    scanLogsResult.rows.forEach(scan => {
      console.log(`\n  📱 Scan ${scan.id}: ${scan.scan_type} at ${scan.scan_timestamp}`);
      console.log(`     Truck: ${scan.truck_number || 'Unknown'}`);
      console.log(`     Location: ${scan.location_name || 'Unknown'}`);
      console.log(`     Trip: ${scan.trip_id || 'None'} (${scan.trip_status || 'N/A'})`);
      console.log(`     Valid: ${scan.is_valid}`);
      if (scan.validation_error) {
        console.log(`     Error: ${scan.validation_error}`);
      }
    });
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
  } finally {
    client.release();
  }
}

// Run the investigation
if (require.main === module) {
  investigateTripProgression()
    .then(() => {
      console.log('\n✅ Trip progression investigation completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Trip progression investigation failed:', error);
      process.exit(1);
    });
}

module.exports = { investigateTripProgression };
