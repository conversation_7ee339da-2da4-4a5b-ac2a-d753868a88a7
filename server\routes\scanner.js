const express = require('express');
const router = express.Router();
const { query, getClient } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const {
  validateTripProgression,
  checkApprovalStatus,
  EXCEPTION_STATES,
  TRIP_STATES
} = require('../utils/exception-flow-manager');
const { notifyExceptionCreated, notifyTripStatusChanged } = require('../websocket');
const { logger, EnhancedLogger } = require('../utils/logger');
const { exceptionFactory } = require('../utils/ExceptionFactory');
const { assignmentValidator, VALIDATION_RESULTS } = require('../utils/AssignmentValidator');
const { dynamicAssignmentAdapter, ADAPTATION_STRATEGIES, CONFIDENCE_LEVELS } = require('../utils/dynamic-assignment-adapter');
const { hybridExceptionManager, HYBRID_EXCEPTION_TYPES } = require('../utils/hybrid-exception-manager');
const { AutoAssignmentCreator } = require('../utils/AutoAssignmentCreator');

// Validation schemas
const scanSchema = Joi.object({
  scan_type: Joi.string().valid('location', 'truck').required(),
  scanned_data: Joi.string().required(),
  location_scan_data: Joi.object().when('scan_type', {
    is: 'truck',
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  ip_address: Joi.string().ip().optional(),
  user_agent: Joi.string().optional(),
  request_id: Joi.string().optional()
});

// Enhanced logging functions
function logDebug(context, message, data = {}) {
  logger.info(message, {
    context: `SCANNER.${context}`,
    data
  });
}

function logError(context, error, additionalData = {}) {
  EnhancedLogger.logScanError(context, error, additionalData.scanData || {}, additionalData.userInfo || {});
}

// @route   POST /api/scanner/scan
// @desc    Process QR code scan with enhanced error handling and transaction management
// @access  Private
router.post('/scan', auth, async (req, res) => {
  const client = await getClient();
  const scanStartTime = Date.now();
  
  try {
    // Validate input
    const { error } = scanSchema.validate(req.body);
    if (error) {
      logError('SCAN_VALIDATION', error, { body: req.body });
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      scan_type,
      scanned_data,
      location_scan_data,
      ip_address,
      user_agent
    } = req.body;

    logDebug('SCAN_REQUEST', 'Processing scan', {
      scan_type,
      user_id: req.user.id,
      ip_address
    });

    // Parse scanned QR data
    let qrData;
    try {
      if (!scanned_data || typeof scanned_data !== 'string') {
        throw new Error('Scanned data is empty or not a string');
      }
      qrData = JSON.parse(scanned_data);
    } catch (parseError) {
      logError('QR_PARSE', parseError, {
        scanData: {
          scan_type,
          scanned_data,
          ip_address,
          user_agent
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: parseError.message === 'Scanned data is empty or not a string'
          ? 'QR code data is empty or invalid'
          : 'QR code data is not valid JSON format'
      });
    }

    // Validate QR code structure
    if (!qrData || typeof qrData !== 'object') {
      logError('QR_VALIDATION', new Error('QR data is not an object'), {
        scanData: { scan_type, scanned_data: JSON.stringify(qrData) },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code data must be a valid object'
      });
    }

    if (!qrData.type || !qrData.id) {
      logError('QR_VALIDATION', new Error('Missing required fields'), {
        scanData: {
          scan_type,
          scanned_data: JSON.stringify(qrData),
          missing_fields: {
            type: !qrData.type,
            id: !qrData.id
          }
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code must contain type and id fields'
      });
    }

    // Verify QR type matches scan type
    if (qrData.type !== scan_type) {
      return res.status(400).json({
        success: false,
        error: 'QR Type Mismatch',
        message: `Expected ${scan_type} QR code but scanned ${qrData.type}`
      });
    }

    // Start database transaction
    await client.query('BEGIN');
    logDebug('TRANSACTION', 'Transaction started');

    let scanResult;

    if (scan_type === 'location') {
      scanResult = await processLocationScan(client, qrData, req.user.id, ip_address, user_agent);
    } else if (scan_type === 'truck') {
      scanResult = await processTruckScan(client, qrData, location_scan_data, req.user.id, ip_address, user_agent);
    }

    // Commit transaction
    await client.query('COMMIT');
    logDebug('TRANSACTION', 'Transaction committed successfully');

    // Log successful scan
    await logScan(client, {
      scan_type,
      scanned_data,
      scanner_user_id: req.user.id,
      scanned_location_id: scanResult.location_id || null,
      scanned_truck_id: scanResult.truck_id || null,
      trip_log_id: scanResult.trip_log_id || null,
      is_valid: true,
      ip_address,
      user_agent
    });

    const processingTime = Date.now() - scanStartTime;
    logDebug('SCAN_SUCCESS', `Scan processed successfully in ${processingTime}ms`, {
      scanResult,
      processingTime
    });

    res.json({
      success: true,
      message: scanResult.message,
      data: scanResult.data,
      next_step: scanResult.next_step,
      processing_time_ms: processingTime
    });

  } catch (error) {
    // Rollback transaction on error
    try {
      await client.query('ROLLBACK');
    } catch (rollbackError) {
      console.error('Failed to rollback transaction:', rollbackError);
    }
    
    logError('SCAN_ERROR', error, {
      scanData: {
        scan_type: req.body.scan_type,
        scanned_data: req.body.scanned_data,
        ip_address: req.body.ip_address,
        user_agent: req.body.user_agent
      },
      userInfo: req.user
    });

    // Log failed scan
    try {
      await logScan(client, {
        scan_type: req.body.scan_type,
        scanned_data: req.body.scanned_data,
        scanner_user_id: req.user?.id,
        is_valid: false,
        validation_error: error.message,
        ip_address: req.body.ip_address,
        user_agent: req.body.user_agent
      });
    } catch (logError) {
      console.error('Failed to log scan error:', logError);
    }

    res.status(500).json({
      success: false,
      error: 'Scan Processing Error',
      message: error.message || 'Failed to process scan',
      processing_time_ms: Date.now() - scanStartTime,
      location_data: error.location_data || null
    });
  } finally {
    client.release();
  }
});

// Process location QR scan with optimized queries
async function processLocationScan(client, qrData, userId, ipAddress, userAgent) {
  // Optimized query with only needed columns
  const locationResult = await client.query(
    `SELECT id, location_code, name, type, coordinates, qr_code_data
     FROM locations
     WHERE location_code = $1 AND status = 'active'`,
    [qrData.id]
  );

  if (locationResult.rows.length === 0) {
    throw new Error(`Location ${qrData.id} not found or inactive`);
  }

  const location = locationResult.rows[0];

  // Verify QR code data matches database (JSONB is already parsed)
  const storedQrData = location.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('QR code data mismatch with database');
  }

  return {
    message: `Location ${location.name} scanned successfully`,
    location_id: location.id,
    data: {
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type,
        coordinates: location.coordinates
      }
    },
    next_step: 'scan_truck'
  };
}

// Process truck QR scan with simplified exception handling
async function processTruckScan(client, qrData, locationScanData, userId, ipAddress, userAgent) {
  // =====================================================================
  // REFACTORED: Simplified exception handling using unified services
  // 1. Use AssignmentValidator for consistent validation
  // 2. Use ExceptionFactory for unified exception creation
  // 3. Maintain strict admin-based assignment workflow
  // =====================================================================

  // Validate truck exists and is active
  const truckResult = await client.query(
    `SELECT id, truck_number, license_plate, qr_code_data
     FROM dump_trucks
     WHERE truck_number = $1 AND status = $2`,
    [qrData.id, 'active']
  );

  if (truckResult.rows.length === 0) {
    const err = new Error(`Truck ${qrData.id} not found or inactive`);
    if (locationScanData) {
      err.location_data = locationScanData;
    }
    throw err;
  }

  const truck = truckResult.rows[0];

  // Verify QR code data (JSONB is already parsed)
  const storedQrData = truck.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid truck QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('Truck QR code data mismatch with database');
  }

  // Get location from previous scan
  const locationResult = await client.query(
    `SELECT id, location_code, name, type 
     FROM locations 
     WHERE location_code = $1`,
    [locationScanData.id]
  );

  if (locationResult.rows.length === 0) {
    const err = new Error('Invalid location context for truck scan');
    err.location_data = locationScanData; // Preserve the original scan data
    throw err;
  }

  const location = locationResult.rows[0];
  // Get current trip and assignment with optimized query
  const tripData = await getCurrentTripAndAssignment(client, truck.id);
  
  // Enhanced debugging for assignment lookup
  EnhancedLogger.logAssignmentLookup(truck.id, {
    truck_number: qrData.id,
    location_id: location.id,
    location_name: location.name,
    has_active_trip: !!tripData.trip,
    has_assignment: !!tripData.assignment,
    trip_status: tripData.trip?.status,
    assignment_id: tripData.assignment?.id,
    statuses: ['assigned', 'in_progress']
  }, tripData.assignment ? [tripData.assignment] : [], 'ASSIGNMENT_LOOKUP');

  // CRITICAL FIX: Always check for valid assignment at current location first
  // This prevents false exceptions when truck has multiple assignments
  const assignmentCheck = await assignmentValidator.hasValidAssignmentForLocation({
    truckNumber: qrData.id,
    locationId: location.id,
    client
  });

  if (assignmentCheck.hasValidAssignment) {
    // Found valid assignment(s) for this location - use the most recent one
    const assignmentAtThisLocation = assignmentCheck.assignments[0];

    EnhancedLogger.logAssignmentLookup(truck.id, {
      truck_number: qrData.id,
      location_id: location.id,
      location_name: location.name,
      location_role: assignmentAtThisLocation.location_role,
      assignments_found: assignmentCheck.assignments.length,
      bypassed_assignment: tripData.assignment?.id || null,
      active_trip_id: tripData.trip?.id || null
    }, [assignmentAtThisLocation], 'ASSIGNMENT_FOUND_FOR_LOCATION');

    // If there's an active trip but it's on a different assignment, handle completion/transition
    if (tripData.trip && tripData.trip.assignment_id !== assignmentAtThisLocation.id) {
      // Active trip is on different assignment - check if we can complete it or start new trip
      if (tripData.trip.status === 'unloading_end' && assignmentAtThisLocation.location_role === 'loading') {
        // Trip completed unloading, now at different loading location - complete current trip and start new one
        logDebug('TRIP_COMPLETION_TRANSITION', 'Completing trip and starting new trip on different assignment', {
          current_trip_id: tripData.trip.id,
          current_assignment_id: tripData.trip.assignment_id,
          new_assignment_id: assignmentAtThisLocation.id,
          location_name: location.name
        });

        // Complete the current trip first
        await client.query(`
          UPDATE trip_logs
          SET status = 'trip_completed',
              trip_completed_time = CURRENT_TIMESTAMP,
              total_duration_minutes = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60,
              updated_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb
          WHERE id = $2
        `, [
          {
            completion_method: 'auto_completed_for_new_assignment',
            completion_reason: 'Trip auto-completed when starting new trip on different assignment',
            completion_location: location.name,
            completion_location_id: location.id
          },
          tripData.trip.id
        ]);

        // Now start new trip on the correct assignment
        return await handleNewTrip(
          client,
          assignmentAtThisLocation,
          location,
          truck,
          userId,
          new Date()
        );
      }
    }

    // No active trip OR active trip is on same assignment - proceed normally
    if (!tripData.trip) {
      // No active trip - create new trip on correct assignment
      return await handleNewTrip(
        client,
        assignmentAtThisLocation,
        location,
        truck,
        userId,
        new Date()
      );
    }
  } else {
    // CRITICAL FIX: No valid assignment found for current location - TRY AUTO-ASSIGNMENT CREATION
    logDebug('AUTO_ASSIGNMENT', 'No valid assignment found for current location - attempting auto-assignment creation', {
      truck_number: truck.truck_number,
      location_name: location.name,
      location_type: location.type,
      location_id: location.id
    });

    const autoAssignmentCreator = new AutoAssignmentCreator();

    try {
      // Check if auto-assignment creation is appropriate
      const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
        truck,
        location,
        client
      });

      if (shouldCreateCheck.shouldCreate) {
        logDebug('AUTO_ASSIGNMENT', 'Creating auto-assignment for unassigned location', {
          truck_number: truck.truck_number,
          location_name: location.name,
          location_type: location.type,
          reason: shouldCreateCheck.reason
        });

        // Create auto-assignment
        const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
          truck,
          location,
          client,
          userId
        });

        EnhancedLogger.logAssignmentLookup(truck.id, {
          truck_number: qrData.id,
          location_id: location.id,
          location_name: location.name,
          assignment_found: true,
          assignment_id: autoAssignment.id,
          auto_created: true
        }, [autoAssignment], 'AUTO_ASSIGNMENT_CREATED');

        // If there's an active trip, complete it first before starting new trip
        if (tripData.trip) {
          logDebug('AUTO_ASSIGNMENT', 'Completing active trip before starting new trip on auto-created assignment', {
            current_trip_id: tripData.trip.id,
            new_assignment_id: autoAssignment.id
          });

          await client.query(`
            UPDATE trip_logs
            SET status = 'trip_completed',
                trip_completed_time = CURRENT_TIMESTAMP,
                total_duration_minutes = EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - loading_start_time))/60,
                updated_at = CURRENT_TIMESTAMP,
                notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb
            WHERE id = $2
          `, [
            {
              completion_method: 'auto_completed_for_auto_assignment',
              completion_reason: 'Trip auto-completed when starting new trip on auto-created assignment',
              completion_location: location.name,
              completion_location_id: location.id
            },
            tripData.trip.id
          ]);
        }

        // Use the newly created assignment to create a trip
        return await handleNewTrip(
          client,
          autoAssignment,
          location,
          truck,
          userId,
          new Date()
        );
      } else {
        logDebug('AUTO_ASSIGNMENT', 'Auto-assignment creation not appropriate - falling back to exception creation', {
          truck_number: truck.truck_number,
          location_name: location.name,
          reason: shouldCreateCheck.reason,
          recommendation: shouldCreateCheck.recommendation
        });
      }
    } catch (autoAssignmentError) {
      logError('AUTO_ASSIGNMENT', autoAssignmentError, {
        truck_number: truck.truck_number,
        location_name: location.name
      });

      // Continue to exception creation if auto-assignment fails
      logDebug('AUTO_ASSIGNMENT', 'Auto-assignment creation failed - falling back to exception creation', {
        truck_number: truck.truck_number,
        location_name: location.name,
        error: autoAssignmentError.message
      });
    }

    // FALLBACK: If auto-assignment creation failed or wasn't appropriate, continue with existing logic
    // Continue to legacy exception creation logic below
  }

  // LEGACY LOGIC: If no active trip and no assignment found, create exception
  // This should only be reached if auto-assignment creation failed or wasn't appropriate
  if (!tripData.assignment && !tripData.trip) {

    // ENHANCED LOGIC: Check if truck has ANY assignments where current location is NOT included
    // This prevents incorrect exception triggering when truck returns to previously assigned locations
    const allAssignmentsForTruck = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `, [qrData.id]);

    if (allAssignmentsForTruck.rows.length > 0) {
      // Check if current location is in ANY of the truck's assignments
      const locationInAssignments = allAssignmentsForTruck.rows.some(assignment =>
        assignment.loading_location_id === location.id || assignment.unloading_location_id === location.id
      );

      if (locationInAssignments) {
        // Current location is already assigned to this truck - NO EXCEPTION NEEDED
        // Find the specific assignment for this location and create a trip
        const assignmentForLocation = allAssignmentsForTruck.rows.find(assignment =>
          assignment.loading_location_id === location.id || assignment.unloading_location_id === location.id
        );

        EnhancedLogger.logAssignmentLookup(truck.id, {
          truck_number: qrData.id,
          location_id: location.id,
          location_name: location.name,
          assignment_found: true,
          assignment_id: assignmentForLocation.id
        }, [assignmentForLocation], 'EXISTING_ASSIGNMENT_FOUND');

        // Use the existing assignment to create a new trip
        return await handleNewTrip(
          client,
          assignmentForLocation,
          location,
          truck,
          userId,
          new Date()
        );
      } else {
        // Current location is NOT in any assignments - TRY AUTO-ASSIGNMENT CREATION FIRST
        const mostRecentAssignment = allAssignmentsForTruck.rows[0];

        // ENHANCED: Try auto-assignment creation before falling back to exception
        const autoAssignmentCreator = new AutoAssignmentCreator();

        try {
          // Check if auto-assignment creation is appropriate
          const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
            truck,
            location,
            client
          });

          if (shouldCreateCheck.shouldCreate) {
            logDebug('AUTO_ASSIGNMENT', 'Creating auto-assignment for unassigned location', {
              truck_number: truck.truck_number,
              location_name: location.name,
              location_type: location.type,
              reason: shouldCreateCheck.reason
            });

            // Create auto-assignment
            const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
              truck,
              location,
              client,
              userId
            });

            EnhancedLogger.logAssignmentLookup(truck.id, {
              truck_number: qrData.id,
              location_id: location.id,
              location_name: location.name,
              assignment_found: true,
              assignment_id: autoAssignment.id,
              auto_created: true
            }, [autoAssignment], 'AUTO_ASSIGNMENT_CREATED');

            // Use the newly created assignment to create a trip
            return await handleNewTrip(
              client,
              autoAssignment,
              location,
              truck,
              userId,
              new Date()
            );
          } else {
            logDebug('AUTO_ASSIGNMENT', 'Auto-assignment creation not appropriate', {
              truck_number: truck.truck_number,
              location_name: location.name,
              reason: shouldCreateCheck.reason,
              recommendation: shouldCreateCheck.recommendation
            });
          }
        } catch (autoAssignmentError) {
          logError('AUTO_ASSIGNMENT', autoAssignmentError, {
            truck_number: truck.truck_number,
            location_name: location.name
          });

          // Continue to exception creation if auto-assignment fails
        }

        // FALLBACK: Create exception if auto-assignment creation failed or wasn't appropriate

        EnhancedLogger.logRouteDeviation(truck.id, {
          id: mostRecentAssignment.loading_location_id,
          name: mostRecentAssignment.loading_location,
          type: 'loading'
        }, location, 'medium');

        // Create route deviation exception - requires admin approval to create new assignment
        // FIXED: Ensure we get the proper expected location name
        let expectedLocationName = mostRecentAssignment.loading_location;
        if (!expectedLocationName) {
          // Fallback: Query the location name directly
          const locationQuery = await client.query(
            'SELECT name FROM locations WHERE id = $1',
            [mostRecentAssignment.loading_location_id]
          );
          expectedLocationName = locationQuery.rows[0]?.name || `Location ID ${mostRecentAssignment.loading_location_id}`;
        }

        return await exceptionFactory.createRouteDeviationException({
          truck,
          expectedLocation: {
            id: mostRecentAssignment.loading_location_id,
            name: expectedLocationName,
            location_code: `LOC-${mostRecentAssignment.loading_location_id}`
          },
          actualLocation: location,
          assignment: mostRecentAssignment,
          userId,
          client
        });
      }
    } else {
      // No assignments exist for this truck at all - Create unassigned trip exception
      return await exceptionFactory.createUnassignedTripException({
        truck,
        location,
        userId,
        client
      });
    }
  }

  // Check if there's an active trip for this assignment
  if (!tripData.trip && tripData.assignment) {
    // No active trip but assignment exists - create new trip
    return await handleNewTrip(
      client,
      tripData.assignment,
      location,
      truck,
      userId,
      new Date()
    );
  }

  // Determine next action based on current state
  const actionResult = await determineNextAction(
    client,
    tripData.trip,
    tripData.assignment,
    location,
    truck,
    userId
  );

  return {
    message: actionResult.message,
    truck_id: truck.id,
    location_id: location.id,
    trip_log_id: actionResult.trip_log_id,
    data: {
      truck: {
        id: truck.id,
        number: truck.truck_number,
        license_plate: truck.license_plate
      },
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type
      },
      trip: actionResult.trip_data,
      assignment: tripData.assignment ? {
        id: tripData.assignment.id,
        loading_location: tripData.assignment.loading_location_name,
        unloading_location: tripData.assignment.unloading_location_name
      } : null
    },
    next_step: actionResult.next_step
  };
}

// FIXED: Query to get current trip and its associated assignment
async function getCurrentTripAndAssignment(client, truckId) {
  // CRITICAL FIX: Find the assignment that has the active trip, not the most recent assignment
  const result = await client.query(`
    WITH current_trip AS (
      SELECT tl.*, tl.assignment_id as trip_assignment_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
      ORDER BY tl.created_at DESC
      LIMIT 1
    ),
    trip_assignment AS (
      -- Get the assignment for the active trip (if any)
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        ct.trip_assignment_id as active_assignment_id
      FROM current_trip ct
      JOIN assignments a ON ct.trip_assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
    ),
    fallback_assignment AS (
      -- Fallback: get most recent assignment if no active trip
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        a.id as active_assignment_id
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status IN ('assigned', 'in_progress')
        AND NOT EXISTS (SELECT 1 FROM current_trip)
      ORDER BY a.created_at DESC
      LIMIT 1
    ),
    combined_assignment AS (
      SELECT * FROM trip_assignment
      UNION ALL
      SELECT * FROM fallback_assignment
      LIMIT 1
    )
    SELECT
      ct.id as trip_id,
      ct.trip_assignment_id,
      ct.trip_number,
      ct.status as trip_status,
      ct.loading_start_time,
      ct.loading_end_time,
      ct.unloading_start_time,
      ct.unloading_end_time,
      ct.is_exception,
      ct.exception_reason,
      ct.actual_loading_location_id,
      ct.actual_unloading_location_id,
      ca.assignment_id,
      ca.truck_id,
      ca.driver_id,
      ca.assignment_status,
      ca.priority,
      ca.expected_loads_per_day,
      ca.loading_location_id,
      ca.loading_location_name,
      ca.unloading_location_id,
      ca.unloading_location_name,
      ca.active_assignment_id
    FROM combined_assignment ca
    LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.assignment_id
  `, [truckId]);

  if (result.rows.length === 0) {
    return { trip: null, assignment: null };
  }

  const row = result.rows[0];
  
  // Extract trip data if exists
  const trip = row.trip_id ? {
    id: row.trip_id,
    assignment_id: row.trip_assignment_id,
    trip_number: row.trip_number,
    status: row.trip_status,
    loading_start_time: row.loading_start_time,
    loading_end_time: row.loading_end_time,
    unloading_start_time: row.unloading_start_time,
    unloading_end_time: row.unloading_end_time,
    is_exception: row.is_exception,
    exception_reason: row.exception_reason,
    actual_loading_location_id: row.actual_loading_location_id,
    actual_unloading_location_id: row.actual_unloading_location_id
  } : null;
  // Extract assignment data
  const assignment = {
    id: row.assignment_id,
    truck_id: row.truck_id,
    driver_id: row.driver_id,
    loading_location_id: row.loading_location_id,
    loading_location_name: row.loading_location_name,
    unloading_location_id: row.unloading_location_id,
    unloading_location_name: row.unloading_location_name,
    status: row.assignment_status,
    priority: row.priority,
    expected_loads_per_day: row.expected_loads_per_day,
    assigned_date: row.assigned_date,
    start_time: row.start_time,
    end_time: row.end_time
  };

  return { trip, assignment };
}

// Enhanced determine next action with better exception handling
async function determineNextAction(client, currentTrip, assignment, location, truck, userId) {
  const now = new Date();
  
  // Check if trip exists
  if (!currentTrip) {
    throw new Error('No active trip found. Please start a new trip by scanning at the assigned loading location.');
  }
  
  // Verify trip state is valid before proceeding
  const validStates = ['loading_start', 'loading_end', 'unloading_start', 'unloading_end', 'exception_pending'];
  if (!validStates.includes(currentTrip.status)) {
    throw new Error(`Invalid trip state: ${currentTrip.status}. Cannot determine next action.`);
  }

  // Use enhanced exception flow validation
  const progressionCheck = await validateTripProgression(client, currentTrip.id);
  
  if (!progressionCheck.canProgress) {
    switch (progressionCheck.nextAction) {
      case 'await_approval':
        return {
          message: 'Exception awaiting approval from supervisor',
          trip_log_id: currentTrip.id,
          trip_data: currentTrip,
          next_step: 'await_approval',
          approval_data: progressionCheck.approval_data
        };
      
      case 'trip_cancelled':
        throw new Error('This trip has been cancelled because the exception was rejected.');
      
      default:
        throw new Error(progressionCheck.reason);
    }
  }

  // If exception was approved and requires status update
  if (progressionCheck.requiresStatusUpdate && currentTrip.status === EXCEPTION_STATES.PENDING) {
    // Per Trip Flow Logic: approved exceptions result in trip completion
    // The truck has already completed the A→B→C cycle
    currentTrip.status = TRIP_STATES.TRIP_COMPLETED;

    // Update assignment reference if changed
    if (progressionCheck.approval_data) {
      const tripNotes = currentTrip.notes || {};
      if (tripNotes.pending_assignment_id) {
        assignment.id = tripNotes.pending_assignment_id;
      }
    }

    // Return completion message since trip is now complete
    return {
      message: 'Exception approved - Trip completed successfully! You can now start a new trip.',
      trip_log_id: currentTrip.id,
      trip_data: currentTrip,
      next_step: 'trip_complete'
    };
  }

  // Check for completed prerequisites before allowing next step
  switch (currentTrip.status) {
    case 'loading_start':
      // Verify we're at the right location type before allowing loading to start/end
      if (location.type !== 'loading') {
        throw new Error(`Cannot perform loading operation at a ${location.type} location. Must be at a loading location.`);
      }
      return await handleLoadingStart(client, currentTrip, assignment, location, now);
    
    case 'loading_end':
      // For loading end, we expect to be at the unloading location
      if (location.type !== 'unloading' && location.id !== assignment.unloading_location_id) {
        // If not at the assigned unloading location, handle as a route deviation
        logDebug('ROUTE_DEVIATION', 'Detected unloading location deviation', {
          expected: assignment.unloading_location_id,
          actual: location.id
        });
      }
      return await handleLoadingEnd(client, currentTrip, assignment, location, userId, now);
    
    case 'unloading_start':
      // Must complete unloading at the same location where it started
      if (location.id !== (currentTrip.actual_unloading_location_id || assignment.unloading_location_id)) {
        throw new Error('Unloading must be completed at the same location where it started');
      }
      return await handleUnloadingStart(client, currentTrip, assignment, location, now);
      case 'unloading_end':
      // For trip completion, check if we're at the assigned loading location for normal flow
      // If at different location, this could be a route deviation (A→B→C pattern)
      if (location.id !== assignment.loading_location_id) {
        // Check if this is at a valid loading location but wrong one (route deviation)
        if (location.type === 'loading') {
          // This is a route deviation: A→B→C pattern detected
          logDebug('ROUTE_DEVIATION_COMPLETION', 'Route deviation detected at completion', {
            assigned_loading: assignment.loading_location_id,
            actual_location: location.id,
            trip_id: currentTrip.id
          });
          
          // Create route deviation for completion at different loading location
          return await createRouteDeviationForExistingAssignment(
            client, assignment, location, truck, userId, now
          );
        } else {
          throw new Error(`Trip can only be completed at a loading location. Currently at ${location.type} location.`);
        }
      }
      
      // Normal completion at assigned loading location (A→B→A pattern)
      return await handleUnloadingEnd(client, currentTrip, assignment, location, now);
    
    default:
      throw new Error(`Invalid trip status: ${currentTrip.status}`);
  }
}

// Handle new trip creation with route deviation detection
async function handleNewTrip(client, assignment, location, truck, userId, now) {
  const tripNumber = await getNextTripNumber(client, assignment.id);

  // Check if starting at assigned loading location
  if (location.id === assignment.loading_location_id) {
    // Normal trip start
    const newTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [assignment.id, tripNumber, 'loading_start', now, location.id, now, now]);

    const newTrip = newTripResult.rows[0];

    // Send notification for trip start
    try {
      notifyTripStatusChanged({
        id: newTrip.id,
        trip_number: tripNumber,
        status: 'loading_start',
        truck_number: truck.truck_number,
        location_name: location.name
      });
    } catch (notifyError) {
      console.error('Failed to send trip notification:', notifyError);
    }

    return {
      message: `Loading started at ${location.name}`,
      trip_log_id: newTrip.id,
      trip_data: newTrip,
      next_step: 'scan_loading_end'
    };
  }

  // Route deviation detected - create exception
  return await createRouteDeviationException(
    client, assignment, location, truck, userId, now, tripNumber
  );
}

// Create route deviation for existing assignment (when truck scans at wrong location)
async function createRouteDeviationForExistingAssignment(client, originalAssignment, actualLocation, truck, userId, now) {
  const tripNumber = await getNextTripNumber(client, originalAssignment.id);
  
  // Check if this truck already has route deviations today
  const existingDeviationsResult = await client.query(`
    SELECT tl.id, tl.exception_reason, tl.actual_loading_location_id, tl.notes
    FROM trip_logs tl 
    JOIN assignments a ON tl.assignment_id = a.id
    WHERE a.truck_id = $1 
    AND tl.is_exception = true 
    AND tl.exception_reason LIKE 'Route deviation%'
    AND DATE(tl.created_at) = CURRENT_DATE
    ORDER BY tl.created_at DESC
    LIMIT 1
  `, [truck.id]);
  
  const hasExistingDeviations = existingDeviationsResult.rows.length > 0;
  const deviationType = hasExistingDeviations ? 'multiple_deviation' : 'first_deviation';
  
  // Create trip with exception_triggered status (not exception_pending yet)
  const newTripResult = await client.query(`
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, loading_start_time,
      actual_loading_location_id, is_exception, exception_reason,
      created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    originalAssignment.id,
    tripNumber,
    'exception_triggered', // Changed from 'exception_pending'
    now,
    actualLocation.id,
    true,
    `Route deviation: Loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}`,
    now,
    now
  ]);

  const newTrip = newTripResult.rows[0];
    // DO NOT create assignment immediately - wait for admin approval
  // Store the proposed assignment details in trip notes for admin review
  let proposedAssignmentId = null;
    // NEW APPROACH: Don't create pending_approval assignments
  // Instead, store the proposed assignment data in trip notes for later creation during approval

  // Ensure we have a valid driver_id for the proposed assignment data
  let driverId = originalAssignment.driver_id;

  // If no driver_id from assignment, find an active driver
  if (!driverId) {
    const defaultDriverResult = await client.query(`
      SELECT id FROM drivers
      WHERE status = 'active'
      ORDER BY created_at ASC
      LIMIT 1
    `);

    if (defaultDriverResult.rows.length === 0) {
      throw new Error('No active drivers available for route deviation assignment');
    }

    driverId = defaultDriverResult.rows[0].id;
  }

  // Store proposed assignment data (don't create actual assignment yet)
  const proposedAssignmentData = {
    truck_id: originalAssignment.truck_id,
    driver_id: driverId,
    loading_location_id: actualLocation.id, // C (Loading location is now the actual detected location)
    unloading_location_id: originalAssignment.unloading_location_id, // B (Keep the same unloading location)
    assigned_date: originalAssignment.assigned_date || now.toISOString().split('T')[0],
    start_time: originalAssignment.start_time,
    end_time: originalAssignment.end_time,
    priority: originalAssignment.priority || 'normal',
    expected_loads_per_day: originalAssignment.expected_loads_per_day || 1,
    assignment_metadata: {
      type: 'route_deviation',
      deviation_count: hasExistingDeviations ? 'multiple' : 'first',
      from_location_id: originalAssignment.loading_location_id,
      to_location_id: actualLocation.id,
      original_assignment_id: originalAssignment.id,
      original_flow: `${originalAssignment.loading_location || 'Unknown Location'} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${originalAssignment.loading_location || 'Unknown Location'}`,
      new_flow: `${actualLocation.name} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${actualLocation.name}`,
      schedule_preserved: true,
      previous_deviation_ids: hasExistingDeviations ? [existingDeviationsResult.rows[0].id] : []
    }
  };

  // No assignment created here - will be created during admin approval
  proposedAssignmentId = null;

  // Set appropriate severity based on deviation history
  const severityLevel = hasExistingDeviations ? 'high' : 'medium';

  // Use hybrid exception manager for intelligent exception handling
  try {
    const hybridResult = await hybridExceptionManager.createHybridException({
      tripLogId: newTrip.id,
      truckNumber: truck.truck_number,
      currentLocationId: actualLocation.id,
      exceptionType: 'Route Deviation',
      exceptionDescription: `Truck ${truck.truck_number} loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}`,
      severity: severityLevel,
      reportedBy: userId,
      originalAssignment
    });

    logDebug('HYBRID_EXCEPTION', 'Hybrid exception created', {
      trip_id: newTrip.id,
      hybrid_type: hybridResult.hybridType,
      auto_approved: hybridResult.autoApproved
    });

  } catch (hybridError) {
    logError('HYBRID_EXCEPTION', hybridError, { trip_id: newTrip.id });

    // Fallback to traditional exception creation
    await client.query(`
      INSERT INTO approvals (
        trip_log_id, exception_type, exception_description,
        requested_at, status, severity,
        reported_by, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      newTrip.id,
      'Route Deviation',
      `Truck ${truck.truck_number} loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}`,
      now,
      'pending',
      severityLevel,
      userId,
      now,
      now
    ]);
  }  // Store the proposed assignment data and flow information in trip metadata
  // PRESERVE LOCATION SCAN DATA - do not reset location scan value
  await client.query(`
    UPDATE trip_logs
    SET notes = $1
    WHERE id = $2
  `, [
    {
      // Store proposed assignment data (no actual assignment created yet)
      proposed_assignment_data: proposedAssignmentData,
      original_assignment_id: originalAssignment.id,
      deviation_type: 'location_flow',
      deviation_count: hasExistingDeviations ? 'multiple' : 'first',
      from_location: originalAssignment.loading_location,
      to_location: actualLocation.name,
      revised_flow_pattern: `${actualLocation.name} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${actualLocation.name}`,
      pattern: `${actualLocation.name} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${actualLocation.name}`,
      location_scan_preserved: true, // Flag to indicate location scan is preserved
      actual_location: {
        id: actualLocation.id,
        code: actualLocation.location_code,
        name: actualLocation.name,
        type: actualLocation.type
      },
      previous_deviation_ids: hasExistingDeviations ? [existingDeviationsResult.rows[0].id] : [],
      // Flag to indicate this uses the new workflow (no pending assignment created)
      workflow_version: 'no_pending_assignment'
    },
    newTrip.id
  ]);

  // Send real-time notification
  try {
    notifyExceptionCreated({
      id: newTrip.id,
      exception_type: 'Route Deviation',
      exception_description: `Truck ${truck.truck_number} loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}`,
      severity: severityLevel,
      message: `Route deviation detected! Loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}. Approval required.`,
      truck_id: truck.id,
      location_id: actualLocation.id,
      trip_log_id: newTrip.id,
      data: {
        truck: {
          id: truck.id,
          number: truck.truck_number,
          license_plate: truck.license_plate
        },
        location: {
          id: actualLocation.id,
          code: actualLocation.location_code,
          name: actualLocation.name,
          type: actualLocation.type
        },
        trip: newTrip,
        assignment: {
          id: originalAssignment.id,
          loading_location: originalAssignment.loading_location,
          unloading_location: originalAssignment.unloading_location || 'Unknown Location',
          status: originalAssignment.status
        },        revised_flow: {
          pattern: `${actualLocation.name} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${actualLocation.name}`,
          proposed_assignment_data: proposedAssignmentData,
          is_multiple_deviation: hasExistingDeviations,
          workflow_version: 'no_pending_assignment'
        }
      }
    });
  } catch (notifyError) {
    console.error('Failed to send notification:', notifyError);
  }

  return {
    message: `Route deviation detected! Loading at ${actualLocation.name} instead of assigned ${originalAssignment.loading_location || 'Unknown Location'}. Approval required.`,
    trip_log_id: newTrip.id,
    trip_data: newTrip,
    revised_flow: {
      pattern: `${actualLocation.name} → ${originalAssignment.unloading_location || 'Unknown Location'} → ${actualLocation.name}`,
      new_assignment_id: proposedAssignmentId
    },
    next_step: 'await_approval'
  };
}

// Create unassigned trip with auto-assignment (for trucks with historical assignments but none today)
async function createUnassignedTripWithAutoAssignment(client, truck, location, userId, now, historicalAssignment) {
  // Use dynamic assignment adapter to analyze patterns and suggest optimal assignment
  try {
    const adaptationAnalysis = await dynamicAssignmentAdapter.analyzeMovementPatterns(
      truck.truck_number,
      location.id,
      { client }
    );

    logDebug('DYNAMIC_ADAPTATION', 'Movement pattern analysis completed', {
      truck_number: truck.truck_number,
      location_id: location.id,
      suggestions_count: adaptationAnalysis.suggestions.length
    });
  } catch (adaptationError) {
    logError('DYNAMIC_ADAPTATION', adaptationError, { truck_number: truck.truck_number });
  }

  // Create new assignment based on historical assignment pattern but with current location
  // Check if an assignment already exists for this exact combination (truck, loading, unloading, date)
  const existingAssignment = await client.query(`    SELECT id FROM assignments
    WHERE truck_id = $1
      AND loading_location_id = $2
      AND unloading_location_id = $3
      AND status IN ('assigned', 'in_progress')
  `, [truck.id, location.id, historicalAssignment.unloading_location_id]);

  let newAssignment;
  
  if (existingAssignment.rows.length === 0) {
    // Ensure we have a valid driver_id
    let driverId = historicalAssignment.driver_id;
    
    // If no driver_id from historical assignment, find an active driver
    if (!driverId) {
      const defaultDriverResult = await client.query(`
        SELECT id FROM drivers 
        WHERE status = 'active' 
        ORDER BY created_at ASC 
        LIMIT 1
      `);
      
      if (defaultDriverResult.rows.length === 0) {
        throw new Error('No active drivers available for auto-assignment');
      }
      
      driverId = defaultDriverResult.rows[0].id;
    }
    
    // Create new assignment with the current location as loading point
    // and the historical unloading location
    const assignment_code = `ASG-${Date.now()}-AUTO`;
    const newAssignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status, priority, 
        expected_loads_per_day, notes, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [      assignment_code,
      truck.id,
      driverId, 
      location.id, // Current location becomes new loading point
      historicalAssignment.unloading_location_id, // Keep historical unloading location
      now.toISOString().split('T')[0],
      'pending_approval', // IMPORTANT: Create in pending status - requires admin approval
      historicalAssignment.priority || 'normal',
      historicalAssignment.expected_loads_per_day || 1,
      JSON.stringify({
        type: 'auto_assignment',
        based_on_historical: true,
        status: 'pending_approval', // Explicit status in notes
        requires_approval: true,
        historical_assignment_id: historicalAssignment.id,
        original_loading: historicalAssignment.loading_location,
        new_loading: location.name
      }),
      now,
      now
    ]);
    
    newAssignment = newAssignmentResult.rows[0];
  } else {
    // Use existing assignment
    const existingAssignmentDetails = await client.query(`
      SELECT a.*, l1.name as loading_location, l2.name as unloading_location
      FROM assignments a
      JOIN locations l1 ON a.loading_location_id = l1.id
      JOIN locations l2 ON a.unloading_location_id = l2.id
      WHERE a.id = $1
    `, [existingAssignment.rows[0].id]);
    
    newAssignment = existingAssignmentDetails.rows[0];
  }
  
  // Create trip with pending exception status
  const tripNumber = await getNextTripNumber(client, newAssignment.id);
  const newTripResult = await client.query(`
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, loading_start_time,
      actual_loading_location_id, is_exception, exception_reason,
      created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    newAssignment.id,
    tripNumber,
    'exception_pending',
    now,
    location.id,
    true,
    `Unassigned trip: Truck ${truck.truck_number} at ${location.name} without today's assignment`,
    now,
    now
  ]);

  const newTrip = newTripResult.rows[0];
  
  // Create approval request
  await client.query(`
    INSERT INTO approvals (
      trip_log_id, exception_type, exception_description,
      requested_at, status, severity,
      reported_by, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
  `, [
    newTrip.id,
    'Unassigned Trip',
    `Auto-created assignment for truck ${truck.truck_number} based on historical pattern`,
    now,
    'pending',
    'medium',
    userId,
    now,
    now
  ]);

  // Store reference to historical assignment in trip notes
  await client.query(`
    UPDATE trip_logs
    SET notes = $1
    WHERE id = $2
  `, [
    {
      historical_assignment_id: historicalAssignment.id,
      auto_created_assignment_id: newAssignment.id,
      deviation_type: 'unassigned_trip',
      historical_loading: historicalAssignment.loading_location,
      current_location: location.name
    },
    newTrip.id
  ]);

  // Send notification
  try {
    notifyExceptionCreated({
      id: newTrip.id,
      exception_type: 'Unassigned Trip',
      description: `Auto-created assignment for truck ${truck.truck_number} at ${location.name}`,
      severity: 'medium',
      trip_log_id: newTrip.id,
      truck_number: truck.truck_number,
      created_at: now.toISOString()
    });
  } catch (notifyError) {
    console.error('Failed to send exception notification:', notifyError);
  }

  return {
    message: `Unassigned trip detected! Auto-created assignment for truck ${truck.truck_number} at ${location.name}. Approval required.`,
    truck_id: truck.id,
    location_id: location.id,
    trip_log_id: newTrip.id,
    assignment_id: newAssignment.id,
    data: {
      truck: {
        id: truck.id,
        number: truck.truck_number,
        license_plate: truck.license_plate
      },
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type
      },
      trip: newTrip,
      assignment: {
        id: newAssignment.id,
        assignment_code: newAssignment.assignment_code,
        loading_location: location.name,
        unloading_location: historicalAssignment.unloading_location || location.name,
        status: newAssignment.status,
        auto_created: true
      }
    },
    next_step: 'await_approval'
  };
}

// Create route deviation exception with auto-assignment
async function createRouteDeviationException(client, assignment, location, truck, userId, now, tripNumber) {
  // Create trip with exception_triggered status (not exception_pending yet)
  const newTripResult = await client.query(`
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, loading_start_time,
      actual_loading_location_id, is_exception, exception_reason,
      created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING *
  `, [
    assignment.id,
    tripNumber,
    'exception_triggered', // Changed from 'exception_pending'
    now,
    location.id,
    true,
    `Route deviation: Loading at ${location.name} instead of assigned location`,
    now,
    now
  ]);

  const newTrip = newTripResult.rows[0];
  // Check if assignment exists for the new location
  const existingAssignment = await client.query(`
    SELECT id FROM assignments 
    WHERE truck_id = $1 
      AND driver_id = $2 
      AND loading_location_id = $3 
      AND assigned_date = CURRENT_DATE
      AND status IN ('assigned', 'in_progress')
  `, [assignment.truck_id, assignment.driver_id, location.id]);

  let newAssignmentId;
  
  if (existingAssignment.rows.length === 0) {
    // Ensure we have a valid driver_id
    let driverId = assignment.driver_id;
    
    // If no driver_id from assignment, find an active driver
    if (!driverId) {
      const defaultDriverResult = await client.query(`
        SELECT id FROM drivers 
        WHERE status = 'active' 
        ORDER BY created_at ASC 
        LIMIT 1
      `);
      
      if (defaultDriverResult.rows.length === 0) {
        throw new Error('No active drivers available for route deviation assignment');
      }
      
      driverId = defaultDriverResult.rows[0].id;
    }
    
    // Create new assignment with current location as loading point
    const assignment_code = `ASG-${Date.now()}-AUTO-UNASSIGNED`;
    const newAssignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id, 
        loading_location_id, unloading_location_id, 
        assigned_date, status, priority, 
        expected_loads_per_day, notes, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      RETURNING *
    `, [      assignment_code,
      truck.id,
      driverId, // Use validated driver_id
      location.id, // Current location becomes new loading point
      assignment.unloading_location_id || location.id, // Use original assignment unloading or current location
      now.toISOString().split('T')[0],
      'pending_approval', // Create in pending status - wait for approval
      'normal',
      1,
      '[Auto-created for route deviation - pending approval]',
      now,
      now
    ]);

    newAssignmentId = newAssignmentResult.rows[0].id;
  } else {
    newAssignmentId = existingAssignment.rows[0].id;
  }
  // Create approval request for the unassigned trip
  await client.query(`
    INSERT INTO approvals (
      trip_log_id, exception_type, exception_description,
      requested_at, status, severity,
      reported_by, created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
  `, [
    newTrip.id,
    'Unassigned Trip',
    `Auto-created assignment for truck ${truck.truck_number} starting at ${location.name}`,
    now,
    'pending',
    'medium',
    userId,
    now,
    now
  ]);

  // Send notification
  try {
    notifyExceptionCreated({
      id: newTrip.id,
      exception_type: 'Unassigned Trip',
      description: `Auto-created assignment for truck ${truck.truck_number} at ${location.name}`,
      severity: 'medium',
      trip_log_id: newTrip.id,
      truck_number: truck.truck_number,
      created_at: now.toISOString()
    });
  } catch (notifyError) {
    console.error('Failed to send exception notification:', notifyError);
  }

  return {
    message: `Unassigned trip detected! Auto-created assignment for truck ${truck.truck_number} at ${location.name}. Approval required.`,
    truck_id: truck.id,
    location_id: location.id,
    trip_log_id: newTrip.id,
    assignment_id: newAssignmentId,
    data: {
      truck: {
        id: truck.id,
        number: truck.truck_number,
        license_plate: truck.license_plate
      },
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type
      },
      trip: newTrip,
      assignment: {
        id: newAssignmentId,
        assignment_code: 'AUTO-CREATED',
        loading_location: location.name,
        unloading_location: 'TBD',
        status: 'pending_approval',
        auto_created: true
      }
    },
    next_step: 'await_approval'
  };
}

// Handle loading start state
async function handleLoadingStart(client, trip, assignment, location, now) {
  // Check if at the correct loading location
  const expectedLocationId = trip.actual_loading_location_id || assignment.loading_location_id;
  
  if (location.id !== expectedLocationId) {
    throw new Error('Must complete loading at the same location where it started');
  }

  // Calculate loading duration
  const loadingDuration = Math.round(
    (now - new Date(trip.loading_start_time)) / (1000 * 60)
  );

  // Update trip to loading_end
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, loading_end_time = $2, loading_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['loading_end', now, loadingDuration, now, trip.id]);

  return {
    message: `Loading completed in ${loadingDuration} minutes. Proceed to unloading location.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'travel_to_unloading'
  };
}

// Handle loading end state
async function handleLoadingEnd(client, trip, assignment, location, userId, now) {
  if (location.id !== assignment.unloading_location_id) {
    if (location.type === 'unloading') {
      // Route deviation for unloading location
      const updatedTrip = await client.query(`
        UPDATE trip_logs 
        SET status = $1, unloading_start_time = $2, actual_unloading_location_id = $3,
            is_exception = true, exception_reason = $4, updated_at = $5
        WHERE id = $6
        RETURNING *
      `, [
        'unloading_start', 
        now, 
        location.id,
        `Route deviation: Unloading at ${location.name} instead of assigned location`,
        now,
        trip.id
      ]);      // Create approval for unloading deviation
      await client.query(`
        INSERT INTO approvals (
          trip_log_id, exception_type, exception_description,
          requested_at, status, severity,
          reported_by, created_at, updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        trip.id,
        'Route Deviation',
        `Unloading at ${location.name} instead of assigned location`,
        now,
        'pending',
        'low',
        userId,
        now,
        now
      ]);

      return {
        message: `Route deviation: Unloading at ${location.name}. Proceeding with unloading.`,
        trip_log_id: trip.id,
        trip_data: updatedTrip.rows[0],
        next_step: 'scan_unloading_end'
      };
    } else {
      throw new Error(`Expected unloading location, but scanned ${location.type} location`);
    }
  }

  // Calculate travel duration
  const travelDuration = Math.round(
    (now - new Date(trip.loading_end_time)) / (1000 * 60)
  );

  // Normal unloading start
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, unloading_start_time = $2, travel_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['unloading_start', now, travelDuration, now, trip.id]);

  return {
    message: `Arrived at unloading location after ${travelDuration} minutes travel. Start unloading.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'scan_unloading_end'
  };
}

// Handle unloading start state
async function handleUnloadingStart(client, trip, assignment, location, now) {
  const expectedLocationId = trip.actual_unloading_location_id || assignment.unloading_location_id;
  
  if (location.id !== expectedLocationId) {
    throw new Error('Must complete unloading at the same location where it started');
  }

  // Calculate unloading duration
  const unloadingDuration = Math.round(
    (now - new Date(trip.unloading_start_time)) / (1000 * 60)
  );

  // Update trip to unloading_end
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, unloading_end_time = $2, unloading_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['unloading_end', now, unloadingDuration, now, trip.id]);

  return {
    message: `Unloading completed in ${unloadingDuration} minutes. Return to loading location or start new trip.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'return_to_loading_or_new_trip'
  };
}

// Enhanced handle unloading end state with strict validation of trip completion
async function handleUnloadingEnd(client, trip, assignment, location, now) {
  // Check if this is an approved deviation or normal flow
  const isApprovedDeviation = trip.is_exception && trip.actual_loading_location_id;
  
  // Verify all required steps have been completed in the proper sequence
  if (!trip.loading_start_time || !trip.loading_end_time || 
      !trip.unloading_start_time || !trip.unloading_end_time) {
    
    // Build detailed missing steps messages
    const missingSteps = [];
    if (!trip.loading_start_time) missingSteps.push("loading start");
    if (!trip.loading_end_time) missingSteps.push("loading end");
    if (!trip.unloading_start_time) missingSteps.push("unloading start");
    if (!trip.unloading_end_time) missingSteps.push("unloading end");
    
    throw new Error(`Cannot complete trip: Missing required steps: ${missingSteps.join(", ")}`);
  }
  
  // Verify the timestamps are in the correct sequence
  if (trip.loading_end_time < trip.loading_start_time || 
      trip.unloading_start_time < trip.loading_end_time ||
      trip.unloading_end_time < trip.unloading_start_time) {
    throw new Error('Trip step timestamps are out of sequence. Cannot complete trip.');
  }
    // For approved deviations, allow completion at any loading location
  if (location.type === 'loading') {
    // Check if this is the correct loading location for trip completion
    const expectedLoadingLocationId = isApprovedDeviation ? 
      trip.actual_loading_location_id : assignment.loading_location_id;
      if (location.id !== expectedLoadingLocationId) {
      // This is a route deviation - truck is at wrong loading location for completion
      logDebug('ROUTE_DEVIATION_COMPLETION', 'Truck at wrong completion location', {
        expected_location_id: expectedLoadingLocationId,
        actual_location_id: location.id,
        location_name: location.name,
        trip_id: trip.id
      });
      
      // Get proper truck data for the route deviation
      const truckResult = await client.query(
        `SELECT id, truck_number, license_plate FROM dump_trucks WHERE id = $1`,
        [assignment.truck_id]
      );
      
      if (truckResult.rows.length === 0) {
        throw new Error('Truck not found for route deviation');
      }
      
      const truck = truckResult.rows[0];
      
      // Create route deviation exception for completion location change
      return await createRouteDeviationForExistingAssignment(
        client, assignment, location, truck, assignment.driver_id, now
      );
    }
    // Calculate total trip duration
    const totalDuration = Math.round(
      (now - new Date(trip.loading_start_time)) / (1000 * 60)
    );

    // Record final location in case of deviation
    const locationNotes = {};
    if (isApprovedDeviation) {
      locationNotes.completion_location_id = location.id;
      locationNotes.completion_location_name = location.name;
    }

    // Complete the trip
    const updatedTrip = await client.query(`
      UPDATE trip_logs 
      SET status = $1, 
          trip_completed_time = $2, 
          total_duration_minutes = $3, 
          updated_at = $4,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $5::jsonb
      WHERE id = $6
      RETURNING *
    `, [
      'trip_completed', 
      now, 
      totalDuration, 
      now, 
      locationNotes,
      trip.id
    ]);

    // Update assignment status if all expected loads completed
    await updateAssignmentStatus(client, trip.assignment_id);
    
    // Log the completed trip with all details
    logDebug('TRIP_COMPLETED', `Trip ${trip.id} completed successfully`, {
      trip_id: trip.id,
      assignment_id: trip.assignment_id,
      loading_duration: trip.loading_duration_minutes,
      travel_duration: trip.travel_duration_minutes,
      unloading_duration: trip.unloading_duration_minutes,
      total_duration: totalDuration,
      is_exception: trip.is_exception
    });

    return {
      message: `Trip completed successfully! Total time: ${totalDuration} minutes.`,
      trip_log_id: trip.id,
      trip_data: updatedTrip.rows[0],
      next_step: 'trip_complete'
    };
  } else {
    throw new Error('Must return to a loading location to complete the trip');
  }
}

// Update assignment status based on completed trips
async function updateAssignmentStatus(client, assignmentId) {
  // First get assignment details without locking
  const assignmentResult = await client.query(`
    SELECT expected_loads_per_day FROM assignments WHERE id = $1
  `, [assignmentId]);
  
  if (assignmentResult.rows.length === 0) return;
  
  const { expected_loads_per_day } = assignmentResult.rows[0];
  
  // Then count completed trips separately
  const tripCountResult = await client.query(`
    SELECT COUNT(*) as completed_trips
    FROM trip_logs 
    WHERE assignment_id = $1 
      AND status = 'trip_completed'
      AND DATE(created_at) = CURRENT_DATE
  `, [assignmentId]);

  const completed_trips = parseInt(tripCountResult.rows[0].completed_trips);
  
  if (completed_trips >= expected_loads_per_day) {
    await client.query(`
      UPDATE assignments 
      SET status = 'completed', end_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [assignmentId]);
  }
}

// Get next trip number with transaction safety
async function getNextTripNumber(client, assignmentId) {
  // Lock the assignment row to prevent race conditions (no aggregate here)
  await client.query(
    `SELECT id FROM assignments WHERE id = $1 FOR UPDATE`,
    [assignmentId]
  );
  
  // Perform aggregate in a separate query (no FOR UPDATE)
  const result = await client.query(
    `SELECT COALESCE(MAX(trip_number), 0) + 1 as next_number
     FROM trip_logs
     WHERE assignment_id = $1`,
    [assignmentId]
  );
  return result.rows[0].next_number;
}

// Map API scan types to database enum values
function mapScanTypeToEnum(apiScanType) {
  const scanTypeMapping = {
    'location': 'location_scan',
    'truck': 'truck_scan'
  };
  return scanTypeMapping[apiScanType] || apiScanType;
}

// Enhanced scan logging with better error handling
async function logScan(client, scanData) {
  try {
    // Map the scan_type to the database enum value
    const dbScanType = mapScanTypeToEnum(scanData.scan_type);

    await client.query(`
      INSERT INTO scan_logs
      (trip_log_id, scan_type, scanned_data, scanned_location_id, scanned_truck_id,
       scanner_user_id, is_valid, validation_error, ip_address, user_agent, scan_timestamp)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
    `, [
      scanData.trip_log_id,
      dbScanType,
      scanData.scanned_data,
      scanData.scanned_location_id,
      scanData.scanned_truck_id,
      scanData.scanner_user_id,
      scanData.is_valid,
      scanData.validation_error,
      scanData.ip_address,
      scanData.user_agent
    ]);
  } catch (error) {
    logError('SCAN_LOG', error, { scanData });
  }
}

// @route   GET /api/scanner/status/:tripId
// @desc    Get current trip status with optimized query
// @access  Private
router.get('/status/:tripId', auth, async (req, res) => {
  try {
    const { tripId } = req.params;

    const result = await query(`
      SELECT 
        tl.*,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location_name, ll.location_code as loading_location_code,
        ul.name as unloading_location_name, ul.location_code as unloading_location_code,
        al.name as actual_loading_location_name,
        aul.name as actual_unloading_location_name,
        dt.truck_number, dt.license_plate,
        d.full_name as driver_name,
        ap.status as approval_status,
        ap.decision_reason as approval_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN approvals ap ON ap.trip_log_id = tl.id
      WHERE tl.id = $1
      ORDER BY ap.created_at DESC
      LIMIT 1
    `, [tripId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Trip not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('GET_TRIP_STATUS', error, { tripId });
    res.status(500).json({
      success: false,
      error: 'Failed to get trip status'
    });
  }
});

// @route   GET /api/scanner/validate-assignment/:truckId
// @desc    Validate if truck has active assignment
// @access  Private
router.get('/validate-assignment/:truckId', auth, async (req, res) => {
  try {
    const { truckId } = req.params;

    const result = await query(`
      SELECT 
        a.*,
        dt.truck_number,
        d.full_name as driver_name,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truckId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No active assignment found for this truck'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('VALIDATE_ASSIGNMENT', error, { truckId });
    res.status(500).json({
      success: false,
      error: 'Failed to validate assignment'
    });  }
});

// Export both the router and the processTruckScan function for testing
module.exports = router;
module.exports.processTruckScan = processTruckScan;