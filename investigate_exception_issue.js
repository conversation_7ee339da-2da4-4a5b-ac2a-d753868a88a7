#!/usr/bin/env node

/**
 * Investigation Script: Route Deviation Exception Issue
 * 
 * This script investigates why DT-100 is still triggering false route deviation exceptions
 * when scanning at Location A despite having a valid assignment.
 */

const { getClient } = require('./server/config/database');

async function investigateExceptionIssue() {
  console.log('🔍 Investigating Route Deviation Exception Issue for DT-100...\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Current assignments for DT-100
    console.log('📋 Step 1: Current Assignments for DT-100');
    console.log('=' .repeat(60));
    const assignmentsResult = await client.query(`
      SELECT 
        a.id as assignment_id,
        a.assignment_code,
        a.truck_id,
        a.status as assignment_status,
        a.loading_location_id,
        a.unloading_location_id,
        a.assigned_date,
        a.created_at,
        ll.id as loading_loc_id,
        ll.name as loading_location_name,
        ll.location_code as loading_location_code,
        ul.id as unloading_loc_id,
        ul.name as unloading_location_name,
        ul.location_code as unloading_location_code
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1
      ORDER BY a.created_at DESC
    `);
    
    console.log(`Found ${assignmentsResult.rows.length} assignments for DT-100:`);
    assignmentsResult.rows.forEach((assignment, index) => {
      console.log(`\n${index + 1}. Assignment ID: ${assignment.assignment_id}`);
      console.log(`   Code: ${assignment.assignment_code}`);
      console.log(`   Status: ${assignment.assignment_status}`);
      console.log(`   Loading: ${assignment.loading_location_name} (ID: ${assignment.loading_location_id}, Code: ${assignment.loading_location_code})`);
      console.log(`   Unloading: ${assignment.unloading_location_name} (ID: ${assignment.unloading_location_id}, Code: ${assignment.unloading_location_code})`);
      console.log(`   Date: ${assignment.assigned_date}`);
      console.log(`   Created: ${assignment.created_at}`);
    });
    
    // Step 2: Check for active assignments specifically
    console.log('\n\n🎯 Step 2: Active Assignments Analysis');
    console.log('=' .repeat(60));
    const activeAssignmentsResult = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1 
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `);
    
    console.log(`Active assignments: ${activeAssignmentsResult.rows.length}`);
    activeAssignmentsResult.rows.forEach(assignment => {
      console.log(`  ✅ ${assignment.assignment_code}: ${assignment.loading_location} (ID:${assignment.loading_location_id}) → ${assignment.unloading_location} (ID:${assignment.unloading_location_id})`);
    });
    
    // Step 3: Check for Location A specifically
    console.log('\n\n📍 Step 3: Location A Assignment Check');
    console.log('=' .repeat(60));
    const locationAResult = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        ll.name as loading_location,
        ul.name as unloading_location,
        'loading' as location_role
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1 
        AND a.status IN ('assigned', 'in_progress')
        AND a.loading_location_id = 1
      UNION
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        ll.name as loading_location,
        ul.name as unloading_location,
        'unloading' as location_role
      FROM assignments a
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1 
        AND a.status IN ('assigned', 'in_progress')
        AND a.unloading_location_id = 1
    `);
    
    if (locationAResult.rows.length > 0) {
      console.log(`✅ Found ${locationAResult.rows.length} assignments for Location A (ID: 1):`);
      locationAResult.rows.forEach(assignment => {
        console.log(`  ✅ ${assignment.assignment_code}: Location A is ${assignment.location_role} location`);
        console.log(`     Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      });
    } else {
      console.log(`❌ NO assignments found for Location A (ID: 1)`);
      console.log(`   This would explain why exceptions are being triggered!`);
    }
    
    // Step 4: Check recent trip logs
    console.log('\n\n📊 Step 4: Recent Trip Logs for DT-100');
    console.log('=' .repeat(60));
    const recentTripsResult = await client.query(`
      SELECT 
        tl.id as trip_id,
        tl.assignment_id,
        tl.status as trip_status,
        tl.is_exception,
        tl.exception_reason,
        tl.created_at,
        a.assignment_code,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = 1
      ORDER BY tl.created_at DESC
      LIMIT 5
    `);
    
    console.log(`Recent trips (last 5):`);
    recentTripsResult.rows.forEach(trip => {
      const exceptionFlag = trip.is_exception ? '🚨' : '✅';
      console.log(`  ${exceptionFlag} Trip ${trip.trip_id}: ${trip.trip_status} (${trip.assignment_code})`);
      if (trip.is_exception) {
        console.log(`     Exception: ${trip.exception_reason}`);
      }
      console.log(`     Route: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`     Created: ${trip.created_at}`);
    });
    
    // Step 5: Check for incomplete trips
    console.log('\n\n🔄 Step 5: Incomplete Trips Check');
    console.log('=' .repeat(60));
    const incompleteTripsResult = await client.query(`
      SELECT 
        tl.id,
        tl.status,
        tl.created_at,
        a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 
        AND tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
    `);
    
    if (incompleteTripsResult.rows.length > 0) {
      console.log(`⚠️  Found ${incompleteTripsResult.rows.length} incomplete trips:`);
      incompleteTripsResult.rows.forEach(trip => {
        console.log(`  🔄 Trip ${trip.id}: ${trip.status} (${trip.assignment_code}) - ${trip.created_at}`);
      });
    } else {
      console.log(`✅ No incomplete trips found - good for fresh start`);
    }
    
    // Step 6: Check recent exceptions
    console.log('\n\n🚨 Step 6: Recent Exception Requests');
    console.log('=' .repeat(60));
    const recentExceptionsResult = await client.query(`
      SELECT 
        ap.id as approval_id,
        ap.trip_log_id,
        ap.exception_type,
        ap.exception_description,
        ap.status as approval_status,
        ap.created_at,
        tl.status as trip_status
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1
      ORDER BY ap.created_at DESC
      LIMIT 3
    `);
    
    console.log(`Recent exceptions (last 3):`);
    recentExceptionsResult.rows.forEach(exception => {
      console.log(`  🚨 Exception ${exception.approval_id}: ${exception.exception_type} (${exception.approval_status})`);
      console.log(`     Description: ${exception.exception_description}`);
      console.log(`     Trip ${exception.trip_log_id}: ${exception.trip_status}`);
      console.log(`     Created: ${exception.created_at}`);
    });
    
    // Step 7: Verify Location A details
    console.log('\n\n📍 Step 7: Location A Details');
    console.log('=' .repeat(60));
    const locationADetailsResult = await client.query(`
      SELECT 
        id,
        location_code,
        name,
        type,
        status
      FROM locations
      WHERE id = 1 OR name ILIKE '%point a%' OR name ILIKE '%location a%'
    `);
    
    console.log(`Location A details:`);
    locationADetailsResult.rows.forEach(location => {
      console.log(`  📍 ID: ${location.id}, Code: ${location.location_code}, Name: ${location.name}`);
      console.log(`     Type: ${location.type}, Status: ${location.status}`);
    });
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
  } finally {
    client.release();
  }
}

// Run the investigation
if (require.main === module) {
  investigateExceptionIssue()
    .then(() => {
      console.log('\n✅ Investigation completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Investigation failed:', error);
      process.exit(1);
    });
}

module.exports = { investigateExceptionIssue };
