#!/usr/bin/env node

/**
 * Dynamic Assignment Adaptation System
 * 
 * This system analyzes truck behavior patterns and automatically adapts assignments
 * to reduce exceptions and improve operational efficiency.
 * 
 * Features:
 * 1. Pattern Analysis: Analyzes historical trip data to identify patterns
 * 2. Confidence Scoring: Calculates confidence scores for route adaptations
 * 3. Automatic Assignment Creation: Creates new assignments based on patterns
 * 4. Exception Reduction: Reduces false exceptions through intelligent adaptation
 */

const { getClient } = require('./server/config/database');

class DynamicAssignmentAdaptationSystem {
  constructor() {
    this.confidenceThreshold = 0.7; // 70% confidence required for auto-adaptation
    this.minTripsForPattern = 3; // Minimum trips to establish a pattern
    this.lookbackDays = 30; // Days to look back for pattern analysis
  }

  /**
   * Analyze truck behavior patterns for the last 30 days
   */
  async analyzePatterns(client, truckId) {
    console.log(`🔍 Analyzing patterns for truck ${truckId}...`);

    const patternResult = await client.query(`
      WITH trip_routes AS (
        SELECT
          -- Use assignment locations if actual locations are not populated
          COALESCE(tl.actual_loading_location_id, a.loading_location_id) as loading_location_id,
          COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) as unloading_location_id,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          tl.created_at,
          tl.status,
          tl.assignment_id,
          a.assignment_code,
          CASE WHEN tl.is_exception THEN 1 ELSE 0 END as is_exception
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN locations ll ON COALESCE(tl.actual_loading_location_id, a.loading_location_id) = ll.id
        LEFT JOIN locations ul ON COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) = ul.id
        WHERE a.truck_id = $1
          AND tl.created_at >= CURRENT_DATE - INTERVAL '30 days'
          AND tl.status = 'trip_completed'
      ),
      route_patterns AS (
        SELECT
          loading_location_id,
          unloading_location_id,
          loading_location_name,
          unloading_location_name,
          COUNT(*) as trip_count,
          SUM(is_exception) as exception_count,
          ROUND(
            (COUNT(*) - SUM(is_exception))::numeric / COUNT(*) * 100, 2
          ) as success_rate,
          MAX(created_at) as last_trip_date,
          MIN(created_at) as first_trip_date,
          STRING_AGG(DISTINCT assignment_code, ', ') as assignment_codes
        FROM trip_routes
        WHERE loading_location_id IS NOT NULL
          AND unloading_location_id IS NOT NULL
        GROUP BY
          loading_location_id,
          unloading_location_id,
          loading_location_name,
          unloading_location_name
        HAVING COUNT(*) >= $2
      )
      SELECT 
        *,
        CASE 
          WHEN success_rate >= 90 AND trip_count >= 5 THEN 'high'
          WHEN success_rate >= 70 AND trip_count >= 3 THEN 'medium'
          WHEN success_rate >= 50 AND trip_count >= 3 THEN 'low'
          ELSE 'insufficient'
        END as confidence_level,
        ROUND(
          (success_rate / 100.0) * 
          LEAST(trip_count / 10.0, 1.0) * 
          (EXTRACT(DAYS FROM (CURRENT_DATE - first_trip_date)) / 30.0), 
          3
        ) as confidence_score
      FROM route_patterns
      ORDER BY confidence_score DESC, trip_count DESC
    `, [truckId, this.minTripsForPattern]);
    
    return patternResult.rows;
  }

  /**
   * Check if a route pattern already has an assignment
   */
  async hasExistingAssignment(client, truckId, loadingLocationId, unloadingLocationId) {
    const result = await client.query(`
      SELECT id, assignment_code, status
      FROM assignments
      WHERE truck_id = $1
        AND loading_location_id = $2
        AND unloading_location_id = $3
        AND status IN ('assigned', 'in_progress')
    `, [truckId, loadingLocationId, unloadingLocationId]);
    
    return result.rows.length > 0 ? result.rows[0] : null;
  }

  /**
   * Create a new assignment based on identified pattern
   */
  async createAdaptiveAssignment(client, truckId, pattern, driverId) {
    console.log(`🎯 Creating adaptive assignment for pattern: ${pattern.loading_location_name} → ${pattern.unloading_location_name}`);
    
    // Generate assignment code
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
    const assignmentCode = `ASG-${timestamp}-${randomSuffix}`;
    
    const result = await client.query(`
      INSERT INTO assignments (
        truck_id,
        driver_id,
        loading_location_id,
        unloading_location_id,
        assignment_code,
        status,
        priority,
        expected_loads_per_day,
        assigned_date,
        start_time,
        created_at,
        updated_at,
        notes
      ) VALUES (
        $1, $2, $3, $4, $5, 'assigned', 'medium', 
        GREATEST(1, ROUND($6::numeric / 7)), -- Estimate loads per day based on weekly pattern
        CURRENT_DATE,
        CURRENT_TIME,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        $7::jsonb
      ) RETURNING *
    `, [
      truckId,
      driverId,
      pattern.loading_location_id,
      pattern.unloading_location_id,
      assignmentCode,
      pattern.trip_count,
      {
        creation_method: 'dynamic_adaptation',
        pattern_analysis: {
          trip_count: pattern.trip_count,
          success_rate: pattern.success_rate,
          confidence_score: pattern.confidence_score,
          confidence_level: pattern.confidence_level,
          first_trip_date: pattern.first_trip_date,
          last_trip_date: pattern.last_trip_date
        },
        adaptation_reason: 'Automatically created based on observed truck behavior patterns'
      }
    ]);
    
    return result.rows[0];
  }

  /**
   * Main adaptation process
   */
  async adaptAssignments(client, truckId) {
    console.log(`🚀 Starting dynamic assignment adaptation for truck ${truckId}...`);
    
    // Get truck and driver information
    const truckResult = await client.query(`
      SELECT dt.id, dt.truck_number, 
             d.id as driver_id, d.full_name as driver_name
      FROM dump_trucks dt
      LEFT JOIN assignments a ON dt.id = a.truck_id AND a.status IN ('assigned', 'in_progress')
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.id = $1
      LIMIT 1
    `, [truckId]);
    
    if (truckResult.rows.length === 0) {
      throw new Error(`Truck ${truckId} not found`);
    }
    
    const truck = truckResult.rows[0];
    console.log(`📋 Analyzing truck: ${truck.truck_number} (Driver: ${truck.driver_name || 'Unassigned'})`);
    
    // Analyze patterns
    const patterns = await this.analyzePatterns(client, truckId);
    console.log(`📊 Found ${patterns.length} route patterns`);
    
    const adaptations = [];
    
    for (const pattern of patterns) {
      console.log(`\n🔍 Evaluating pattern: ${pattern.loading_location_name} → ${pattern.unloading_location_name}`);
      console.log(`   Trips: ${pattern.trip_count}, Success Rate: ${pattern.success_rate}%, Confidence: ${pattern.confidence_score}`);
      
      // Check if assignment already exists
      const existingAssignment = await this.hasExistingAssignment(
        client,
        truckId,
        pattern.loading_location_id,
        pattern.unloading_location_id
      );
      
      if (existingAssignment) {
        console.log(`   ✅ Assignment already exists: ${existingAssignment.assignment_code} (${existingAssignment.status})`);
        continue;
      }
      
      // Check if pattern meets confidence threshold
      if (pattern.confidence_score >= this.confidenceThreshold) {
        console.log(`   🎯 Pattern meets confidence threshold (${pattern.confidence_score} >= ${this.confidenceThreshold})`);
        
        if (truck.driver_id) {
          try {
            const newAssignment = await this.createAdaptiveAssignment(client, truckId, pattern, truck.driver_id);
            console.log(`   ✅ Created adaptive assignment: ${newAssignment.assignment_code}`);
            
            adaptations.push({
              pattern,
              assignment: newAssignment,
              action: 'created'
            });
          } catch (error) {
            console.log(`   ❌ Failed to create assignment: ${error.message}`);
          }
        } else {
          console.log(`   ⚠️  Cannot create assignment: No driver assigned to truck`);
        }
      } else {
        console.log(`   ⏳ Pattern below confidence threshold (${pattern.confidence_score} < ${this.confidenceThreshold})`);
      }
    }
    
    return {
      truck: truck,
      patterns: patterns,
      adaptations: adaptations
    };
  }

  /**
   * Generate adaptation report
   */
  generateReport(adaptationResult) {
    const { truck, patterns, adaptations } = adaptationResult;
    
    console.log(`\n📊 DYNAMIC ASSIGNMENT ADAPTATION REPORT`);
    console.log(`${'='.repeat(60)}`);
    console.log(`Truck: ${truck.truck_number}`);
    console.log(`Driver: ${truck.driver_name || 'Unassigned'}`);
    console.log(`Analysis Period: Last ${this.lookbackDays} days`);
    console.log(`Confidence Threshold: ${this.confidenceThreshold * 100}%`);
    
    console.log(`\n📈 PATTERN ANALYSIS:`);
    patterns.forEach((pattern, index) => {
      console.log(`  ${index + 1}. ${pattern.loading_location_name} → ${pattern.unloading_location_name}`);
      console.log(`     Trips: ${pattern.trip_count}, Success: ${pattern.success_rate}%, Confidence: ${pattern.confidence_score}`);
      console.log(`     Level: ${pattern.confidence_level}, Period: ${pattern.first_trip_date} to ${pattern.last_trip_date}`);
    });
    
    console.log(`\n🎯 ADAPTATIONS MADE:`);
    if (adaptations.length > 0) {
      adaptations.forEach((adaptation, index) => {
        console.log(`  ${index + 1}. Created Assignment: ${adaptation.assignment.assignment_code}`);
        console.log(`     Route: ${adaptation.pattern.loading_location_name} → ${adaptation.pattern.unloading_location_name}`);
        console.log(`     Confidence: ${adaptation.pattern.confidence_score} (${adaptation.pattern.confidence_level})`);
        console.log(`     Expected Loads/Day: ${adaptation.assignment.expected_loads_per_day}`);
      });
    } else {
      console.log(`  No new assignments created (existing assignments cover patterns or confidence too low)`);
    }
    
    console.log(`\n✅ EXPECTED BENEFITS:`);
    console.log(`  - Reduced false route deviation exceptions`);
    console.log(`  - Improved operational efficiency`);
    console.log(`  - Automatic adaptation to changing routes`);
    console.log(`  - Data-driven assignment management`);
    
    return {
      summary: {
        truck_number: truck.truck_number,
        patterns_analyzed: patterns.length,
        adaptations_created: adaptations.length,
        high_confidence_patterns: patterns.filter(p => p.confidence_level === 'high').length,
        medium_confidence_patterns: patterns.filter(p => p.confidence_level === 'medium').length
      },
      adaptations: adaptations
    };
  }
}

// Export for use in other modules
module.exports = { DynamicAssignmentAdaptationSystem };

// CLI execution
if (require.main === module) {
  async function runAdaptation() {
    const system = new DynamicAssignmentAdaptationSystem();
    const client = await getClient();
    
    try {
      // Run adaptation for DT-100 (truck_id = 1)
      const result = await system.adaptAssignments(client, 1);
      const report = system.generateReport(result);
      
      console.log(`\n🎉 Dynamic Assignment Adaptation completed successfully!`);
      
    } catch (error) {
      console.error('❌ Adaptation failed:', error.message);
      console.error('Stack trace:', error.stack);
    } finally {
      client.release();
    }
  }
  
  runAdaptation()
    .then(() => {
      console.log('\n✅ Dynamic assignment adaptation completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Dynamic assignment adaptation failed:', error);
      process.exit(1);
    });
}
