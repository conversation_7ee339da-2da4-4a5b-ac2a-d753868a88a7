#!/usr/bin/env node

/**
 * Test Multi-Assignment Validation
 * 
 * This script tests the assignment validation logic for trucks with multiple assignments
 * to identify why false route deviation exceptions are being generated.
 */

const { getClient } = require('./server/config/database');

async function testMultiAssignmentValidation() {
  console.log('🧪 Testing Multi-Assignment Validation for DT-100\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Check DT-100's current assignments
    console.log('📋 Step 1: Current Assignments for DT-100');
    console.log('=' .repeat(60));
    
    const assignmentsResult = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        a.loading_location_id,
        a.unloading_location_id,
        ll.name as loading_location,
        ll.location_code as loading_code,
        ul.name as unloading_location,
        ul.location_code as unloading_code,
        a.created_at
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `);
    
    console.log(`DT-100 has ${assignmentsResult.rows.length} active assignments:`);
    assignmentsResult.rows.forEach((assignment, index) => {
      console.log(`\n  ${index + 1}. Assignment ${assignment.id} (${assignment.assignment_code})`);
      console.log(`     Status: ${assignment.status}`);
      console.log(`     Loading: ${assignment.loading_location} (ID: ${assignment.loading_location_id})`);
      console.log(`     Unloading: ${assignment.unloading_location} (ID: ${assignment.unloading_location_id})`);
      console.log(`     Created: ${assignment.created_at}`);
    });
    
    // Step 2: Test location validation for each assignment
    console.log('\n🔍 Step 2: Testing Location Validation Logic');
    console.log('=' .repeat(60));
    
    // Test Point A (should match Assignment 32)
    console.log('\n🎯 Testing Point A (Main Loading Site):');
    const pointAResult = await client.query(`
      SELECT
        a.id as assignment_id,
        a.assignment_code,
        ll.name as loading_location,
        ul.name as unloading_location,
        CASE
          WHEN a.loading_location_id = 1 THEN 'loading'
          WHEN a.unloading_location_id = 1 THEN 'unloading'
          ELSE 'unknown'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND (a.loading_location_id = 1 OR a.unloading_location_id = 1)
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `);
    
    if (pointAResult.rows.length > 0) {
      console.log(`✅ Point A validation: Found ${pointAResult.rows.length} matching assignment(s)`);
      pointAResult.rows.forEach(assignment => {
        console.log(`   Assignment ${assignment.assignment_id}: ${assignment.loading_location} → ${assignment.unloading_location} (${assignment.location_role})`);
      });
    } else {
      console.log(`❌ Point A validation: No assignments found`);
    }
    
    // Test POINT C (should match Assignment 63)
    console.log('\n🎯 Testing POINT C - LOADING:');
    const pointCResult = await client.query(`
      SELECT
        a.id as assignment_id,
        a.assignment_code,
        ll.name as loading_location,
        ul.name as unloading_location,
        CASE
          WHEN a.loading_location_id = 4 THEN 'loading'
          WHEN a.unloading_location_id = 4 THEN 'unloading'
          ELSE 'unknown'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND (a.loading_location_id = 4 OR a.unloading_location_id = 4)
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
    `);
    
    if (pointCResult.rows.length > 0) {
      console.log(`✅ POINT C validation: Found ${pointCResult.rows.length} matching assignment(s)`);
      pointCResult.rows.forEach(assignment => {
        console.log(`   Assignment ${assignment.assignment_id}: ${assignment.loading_location} → ${assignment.unloading_location} (${assignment.location_role})`);
      });
    } else {
      console.log(`❌ POINT C validation: No assignments found`);
    }
    
    // Step 3: Test the current assignment validation logic
    console.log('\n🔧 Step 3: Testing Current Assignment Validation Logic');
    console.log('=' .repeat(60));
    
    // Simulate the hasValidAssignmentForLocation function
    console.log('\nTesting hasValidAssignmentForLocation for POINT C:');
    const validationResult = await client.query(`
      SELECT
        a.*,
        ll.name as loading_location,
        ul.name as unloading_location,
        d.full_name as driver_name,
        d.employee_id as driver_employee_id,
        CASE
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'unknown'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, ['DT-100', 4]); // 4 = POINT C location ID
    
    if (validationResult.rows.length > 0) {
      const assignment = validationResult.rows[0];
      console.log(`✅ Validation successful for POINT C:`);
      console.log(`   Assignment ID: ${assignment.id}`);
      console.log(`   Assignment Code: ${assignment.assignment_code}`);
      console.log(`   Location Role: ${assignment.location_role}`);
      console.log(`   Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`   Driver: ${assignment.driver_name} (${assignment.driver_employee_id})`);
    } else {
      console.log(`❌ Validation failed for POINT C - no assignment found`);
    }
    
    // Step 4: Test the fallback assignment logic
    console.log('\n🔄 Step 4: Testing Fallback Assignment Logic');
    console.log('=' .repeat(60));
    
    // Test getCurrentTripAndAssignment fallback logic
    const fallbackResult = await client.query(`
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        a.id as active_assignment_id
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = 'DT-100'
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `);
    
    if (fallbackResult.rows.length > 0) {
      const assignment = fallbackResult.rows[0];
      console.log(`Fallback assignment (most recent):`);
      console.log(`   Assignment ID: ${assignment.assignment_id}`);
      console.log(`   Route: ${assignment.loading_location_name} → ${assignment.unloading_location_name}`);
      console.log(`   Status: ${assignment.assignment_status}`);
      console.log(`   Created: ${assignment.assignment_created_at}`);
      
      // Check if this assignment matches POINT C
      if (assignment.loading_location_id === 4 || assignment.unloading_location_id === 4) {
        console.log(`✅ Fallback assignment includes POINT C`);
      } else {
        console.log(`❌ Fallback assignment does NOT include POINT C`);
        console.log(`   This could cause false route deviation exceptions!`);
      }
    }
    
    // Step 5: Identify the root cause
    console.log('\n🎯 Step 5: Root Cause Analysis');
    console.log('=' .repeat(60));
    
    console.log('ISSUE ANALYSIS:');
    console.log('1. DT-100 has multiple assignments (32 and 63)');
    console.log('2. Assignment 32: Point A → Point B');
    console.log('3. Assignment 63: POINT C → Point B');
    console.log('4. When truck scans at POINT C:');
    console.log('   - hasValidAssignmentForLocation should find Assignment 63');
    console.log('   - If it fails, fallback logic uses most recent assignment');
    console.log('   - If most recent assignment is 32 (Point A → Point B), POINT C appears invalid');
    console.log('   - This triggers false route deviation exception');
    
    console.log('\nSOLUTION NEEDED:');
    console.log('1. Ensure hasValidAssignmentForLocation works correctly');
    console.log('2. Improve fallback logic to consider location-specific assignments');
    console.log('3. Enhance assignment selection to prioritize location match');
    console.log('4. Fix "Unknown Location" error in exception messages');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testMultiAssignmentValidation()
    .then(() => {
      console.log('\n✅ Multi-assignment validation test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Multi-assignment validation test failed:', error);
      process.exit(1);
    });
}

module.exports = { testMultiAssignmentValidation };
