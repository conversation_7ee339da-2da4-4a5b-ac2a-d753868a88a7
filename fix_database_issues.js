#!/usr/bin/env node

/**
 * Database Fix Script: Resolve Assignment Validation Issues
 * 
 * This script fixes the database issues that are causing false exceptions
 * for trucks with valid assignments.
 */

const { getClient } = require('./server/config/database');

async function fixDatabaseIssues() {
  console.log('🔧 Fixing Database Issues for Assignment Validation...\n');
  
  const client = await getClient();
  
  try {
    await client.query('BEGIN');
    
    // Fix 1: Complete the hanging trip (Trip 37: unloading_end)
    console.log('🔄 Fix 1: Completing hanging trip (Trip 37)...');
    const completeHangingTripResult = await client.query(`
      UPDATE trip_logs 
      SET status = 'trip_completed',
          trip_completed_time = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = 37 AND status = 'unloading_end'
      RETURNING id, status
    `);
    
    if (completeHangingTripResult.rows.length > 0) {
      console.log(`✅ Trip 37 completed successfully`);
    } else {
      console.log(`ℹ️  Trip 37 was already completed or not found`);
    }
    
    // Fix 2: Cancel the pending exception trip (Trip 40)
    console.log('🚫 Fix 2: Cancelling pending exception trip (Trip 40)...');
    const cancelPendingTripResult = await client.query(`
      UPDATE trip_logs
      SET status = 'cancelled',
          updated_at = CURRENT_TIMESTAMP
      WHERE id = 40 AND status = 'exception_pending'
      RETURNING id, status
    `);
    
    if (cancelPendingTripResult.rows.length > 0) {
      console.log(`✅ Trip 40 cancelled successfully`);
    } else {
      console.log(`ℹ️  Trip 40 was already cancelled or not found`);
    }
    
    // Fix 3: Reject the corresponding approval (Exception 26)
    console.log('❌ Fix 3: Rejecting pending approval (Exception 26)...');
    const rejectApprovalResult = await client.query(`
      UPDATE approvals 
      SET status = 'rejected',
          reviewed_by = 1,
          reviewed_at = CURRENT_TIMESTAMP,
          notes = 'System fix: Truck DT-100 has valid assignment for Point A (Assignment 32: ASG-1751022803825-X3HG31). Exception was incorrectly triggered due to assignment selection logic issue.'
      WHERE id = 26 AND status = 'pending'
      RETURNING id, status
    `);
    
    if (rejectApprovalResult.rows.length > 0) {
      console.log(`✅ Exception 26 rejected successfully`);
    } else {
      console.log(`ℹ️  Exception 26 was already processed or not found`);
    }
    
    // Fix 4: Verify no other blocking issues
    console.log('🔍 Fix 4: Checking for other blocking issues...');
    
    // Check for other pending exceptions for DT-100
    const otherPendingResult = await client.query(`
      SELECT ap.id, ap.exception_type, tl.id as trip_id, tl.status as trip_status
      FROM approvals ap
      JOIN trip_logs tl ON ap.trip_log_id = tl.id
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 AND ap.status = 'pending'
    `);
    
    if (otherPendingResult.rows.length > 0) {
      console.log(`⚠️  Found ${otherPendingResult.rows.length} other pending exceptions:`);
      otherPendingResult.rows.forEach(exception => {
        console.log(`   🔴 Exception ${exception.id}: ${exception.exception_type} (Trip ${exception.trip_id})`);
      });
    } else {
      console.log(`✅ No other pending exceptions found`);
    }
    
    // Check for other incomplete trips for DT-100
    const incompleteTripsResult = await client.query(`
      SELECT tl.id, tl.status, a.assignment_code
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = 1 
        AND tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
    `);
    
    if (incompleteTripsResult.rows.length > 0) {
      console.log(`⚠️  Found ${incompleteTripsResult.rows.length} incomplete trips:`);
      incompleteTripsResult.rows.forEach(trip => {
        console.log(`   🔄 Trip ${trip.id}: ${trip.status} (${trip.assignment_code})`);
      });
    } else {
      console.log(`✅ No incomplete trips found`);
    }
    
    await client.query('COMMIT');
    
    console.log('\n🎉 Database fixes completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('  ✅ Completed hanging trip (Trip 37)');
    console.log('  ✅ Cancelled pending exception trip (Trip 40)');
    console.log('  ✅ Rejected incorrect approval (Exception 26)');
    console.log('\n🚀 DT-100 should now be able to scan at Point A without triggering false exceptions!');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ Database fix failed:', error.message);
    throw error;
  } finally {
    client.release();
  }
}

// Run the fix
if (require.main === module) {
  fixDatabaseIssues()
    .then(() => {
      console.log('\n✅ Database fix completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Database fix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixDatabaseIssues };
