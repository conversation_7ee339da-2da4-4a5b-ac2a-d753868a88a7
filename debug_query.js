#!/usr/bin/env node

/**
 * Debug Query: Test the getCurrentTripAndAssignment query directly
 */

const { getClient } = require('./server/config/database');

async function debugQuery() {
  console.log('🔍 Debugging getCurrentTripAndAssignment Query...\n');
  
  const client = await getClient();
  
  try {
    const truckId = 1; // DT-100
    
    // Test each CTE separately
    console.log('📋 Step 1: Testing current_trip CTE');
    const currentTripResult = await client.query(`
      SELECT tl.*, tl.assignment_id as trip_assignment_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
      ORDER BY tl.created_at DESC
      LIMIT 1
    `, [truckId]);
    
    if (currentTripResult.rows.length > 0) {
      const trip = currentTripResult.rows[0];
      console.log(`✅ Found active trip: ${trip.id} (${trip.status}) on assignment ${trip.assignment_id}`);
    } else {
      console.log('❌ No active trip found');
    }
    
    console.log('\n📋 Step 2: Testing trip_assignment CTE');
    if (currentTripResult.rows.length > 0) {
      const trip = currentTripResult.rows[0];
      const tripAssignmentResult = await client.query(`
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          a.id as active_assignment_id
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.id = $1
      `, [trip.assignment_id]);

      if (tripAssignmentResult.rows.length > 0) {
        const assignment = tripAssignmentResult.rows[0];
        console.log(`✅ Found trip assignment: ${assignment.assignment_id}`);
        console.log(`   Route: ${assignment.loading_location_name} → ${assignment.unloading_location_name}`);
      } else {
        console.log('❌ No assignment found for active trip');
      }
    }
    
    console.log('\n📋 Step 3: Testing fallback_assignment CTE');
    const fallbackAssignmentResult = await client.query(`
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        a.id as active_assignment_id
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status IN ('assigned', 'in_progress')
        AND NOT EXISTS (
          SELECT 1 FROM trip_logs tl2
          JOIN assignments a2 ON tl2.assignment_id = a2.id
          WHERE a2.truck_id = $1
            AND tl2.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
        )
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truckId]);
    
    console.log(`Fallback assignments found: ${fallbackAssignmentResult.rows.length}`);
    
    console.log('\n📋 Step 4: Testing the full fixed query');
    const fullQueryResult = await client.query(`
      WITH current_trip AS (
        SELECT tl.*, tl.assignment_id as trip_assignment_id
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = $1
          AND tl.status NOT IN ('trip_completed', 'cancelled', 'exception_pending')
        ORDER BY tl.created_at DESC
        LIMIT 1
      ),
      trip_assignment AS (
        -- Get the assignment for the active trip (if any)
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          ct.trip_assignment_id as active_assignment_id
        FROM current_trip ct
        JOIN assignments a ON ct.trip_assignment_id = a.id
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
      ),
      fallback_assignment AS (
        -- Fallback: get most recent assignment if no active trip
        SELECT
          a.id as assignment_id,
          a.truck_id,
          a.driver_id,
          a.status as assignment_status,
          a.priority,
          a.expected_loads_per_day,
          a.assigned_date,
          a.start_time,
          a.end_time,
          a.created_at as assignment_created_at,
          ll.id as loading_location_id,
          ll.name as loading_location_name,
          ul.id as unloading_location_id,
          ul.name as unloading_location_name,
          a.id as active_assignment_id
        FROM assignments a
        JOIN locations ll ON a.loading_location_id = ll.id
        JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.truck_id = $1
          AND a.status IN ('assigned', 'in_progress')
          AND NOT EXISTS (SELECT 1 FROM current_trip)
        ORDER BY a.created_at DESC
        LIMIT 1
      ),
      combined_assignment AS (
        SELECT * FROM trip_assignment
        UNION ALL
        SELECT * FROM fallback_assignment
        LIMIT 1
      )
      SELECT
        ct.id as trip_id,
        ct.trip_assignment_id,
        ct.trip_number,
        ct.status as trip_status,
        ct.loading_start_time,
        ct.loading_end_time,
        ct.unloading_start_time,
        ct.unloading_end_time,
        ct.is_exception,
        ct.exception_reason,
        ct.actual_loading_location_id,
        ct.actual_unloading_location_id,
        ca.assignment_id,
        ca.truck_id,
        ca.driver_id,
        ca.assignment_status,
        ca.priority,
        ca.expected_loads_per_day,
        ca.loading_location_id,
        ca.loading_location_name,
        ca.unloading_location_id,
        ca.unloading_location_name,
        ca.active_assignment_id
      FROM combined_assignment ca
      LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.assignment_id
    `, [truckId]);
    
    if (fullQueryResult.rows.length > 0) {
      const result = fullQueryResult.rows[0];
      console.log(`✅ Full query result:`);
      console.log(`   Trip ID: ${result.trip_id}`);
      console.log(`   Trip Status: ${result.trip_status}`);
      console.log(`   Assignment ID: ${result.assignment_id}`);
      console.log(`   Assignment Route: ${result.loading_location_name} → ${result.unloading_location_name}`);
    } else {
      console.log('❌ Full query returned no results');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the debug
if (require.main === module) {
  debugQuery()
    .then(() => {
      console.log('\n✅ Debug completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { debugQuery };
