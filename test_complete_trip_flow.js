#!/usr/bin/env node

/**
 * Test Complete Trip Flow: Test the entire trip progression from loading_start to trip_completed
 */

const { getClient } = require('./server/config/database');

async function testCompleteTripFlow() {
  console.log('🧪 Testing Complete Trip Flow: loading_start → loading_end → unloading_start → unloading_end → trip_completed\n');
  
  const client = await getClient();
  
  try {
    // Step 1: Clean up and create a fresh trip
    console.log('🧹 Step 1: Setting up fresh trip for testing');
    console.log('=' .repeat(60));
    
    // Complete any existing incomplete trips
    await client.query(`
      UPDATE trip_logs 
      SET status = 'trip_completed', trip_completed_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id IN (
        SELECT tl.id 
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        WHERE a.truck_id = 1 
          AND tl.status NOT IN ('trip_completed', 'cancelled')
      )
    `);
    
    // Create a new trip in loading_start status
    const loadingStartTime = new Date();
    const newTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id, created_at, updated_at
      )
      VALUES (32, 999, 'loading_start', $1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `, [loadingStartTime]);
    
    const testTrip = newTripResult.rows[0];
    console.log(`✅ Created test trip: ${testTrip.id} in loading_start status`);
    
    // Step 2: Test loading_start → loading_end progression
    console.log('\n🔄 Step 2: Testing loading_start → loading_end');
    console.log('=' .repeat(60));
    
    const loadingDuration = 5; // 5 minutes
    const loadingEndTime = new Date(loadingStartTime.getTime() + loadingDuration * 60 * 1000);
    
    const loadingEndResult = await client.query(`
      UPDATE trip_logs 
      SET status = 'loading_end', loading_end_time = $1, loading_duration_minutes = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [loadingEndTime, loadingDuration, testTrip.id]);
    
    if (loadingEndResult.rows.length > 0) {
      console.log(`✅ Successfully progressed to loading_end`);
      console.log(`   Loading duration: ${loadingDuration} minutes`);
    } else {
      console.log(`❌ Failed to progress to loading_end`);
      return;
    }
    
    // Step 3: Test loading_end → unloading_start progression
    console.log('\n🚛 Step 3: Testing loading_end → unloading_start');
    console.log('=' .repeat(60));
    
    const travelDuration = 10; // 10 minutes travel time
    const unloadingStartTime = new Date(loadingEndTime.getTime() + travelDuration * 60 * 1000);
    
    const unloadingStartResult = await client.query(`
      UPDATE trip_logs 
      SET status = 'unloading_start', 
          unloading_start_time = $1, 
          travel_duration_minutes = $2,
          actual_unloading_location_id = 2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [unloadingStartTime, travelDuration, testTrip.id]);
    
    if (unloadingStartResult.rows.length > 0) {
      console.log(`✅ Successfully progressed to unloading_start`);
      console.log(`   Travel duration: ${travelDuration} minutes`);
    } else {
      console.log(`❌ Failed to progress to unloading_start`);
      return;
    }
    
    // Step 4: Test unloading_start → unloading_end progression
    console.log('\n📦 Step 4: Testing unloading_start → unloading_end');
    console.log('=' .repeat(60));
    
    const unloadingDuration = 3; // 3 minutes
    const unloadingEndTime = new Date(unloadingStartTime.getTime() + unloadingDuration * 60 * 1000);
    
    const unloadingEndResult = await client.query(`
      UPDATE trip_logs 
      SET status = 'unloading_end', 
          unloading_end_time = $1, 
          unloading_duration_minutes = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [unloadingEndTime, unloadingDuration, testTrip.id]);
    
    if (unloadingEndResult.rows.length > 0) {
      console.log(`✅ Successfully progressed to unloading_end`);
      console.log(`   Unloading duration: ${unloadingDuration} minutes`);
    } else {
      console.log(`❌ Failed to progress to unloading_end`);
      return;
    }
    
    // Step 5: Test unloading_end → trip_completed progression
    console.log('\n🏁 Step 5: Testing unloading_end → trip_completed');
    console.log('=' .repeat(60));

    const returnDuration = 8; // 8 minutes return time
    const tripCompletedTime = new Date(unloadingEndTime.getTime() + returnDuration * 60 * 1000);
    const totalDuration = loadingDuration + travelDuration + unloadingDuration + returnDuration;

    const tripCompletedResult = await client.query(`
      UPDATE trip_logs
      SET status = 'trip_completed',
          trip_completed_time = $1,
          total_duration_minutes = $2,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [tripCompletedTime, totalDuration, testTrip.id]);
    
    if (tripCompletedResult.rows.length > 0) {
      console.log(`✅ Successfully progressed to trip_completed`);
      console.log(`   Total trip duration: ${totalDuration} minutes`);
    } else {
      console.log(`❌ Failed to progress to trip_completed`);
      return;
    }
    
    // Step 6: Verify complete trip data
    console.log('\n📊 Step 6: Verifying Complete Trip Data');
    console.log('=' .repeat(60));
    
    const finalTripResult = await client.query(`
      SELECT 
        tl.*,
        a.assignment_code,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.id = $1
    `, [testTrip.id]);
    
    if (finalTripResult.rows.length > 0) {
      const finalTrip = finalTripResult.rows[0];
      console.log(`✅ Complete trip verification:`);
      console.log(`   Trip ID: ${finalTrip.id}`);
      console.log(`   Status: ${finalTrip.status}`);
      console.log(`   Assignment: ${finalTrip.assignment_code}`);
      console.log(`   Route: ${finalTrip.loading_location} → ${finalTrip.unloading_location}`);
      console.log(`   Loading: ${finalTrip.loading_start_time} → ${finalTrip.loading_end_time} (${finalTrip.loading_duration_minutes} min)`);
      console.log(`   Travel: ${finalTrip.loading_end_time} → ${finalTrip.unloading_start_time} (${finalTrip.travel_duration_minutes} min)`);
      console.log(`   Unloading: ${finalTrip.unloading_start_time} → ${finalTrip.unloading_end_time} (${finalTrip.unloading_duration_minutes} min)`);
      console.log(`   Completed: ${finalTrip.trip_completed_time}`);
      console.log(`   Total Duration: ${finalTrip.total_duration_minutes} minutes`);
      
      // Verify all timestamps are present
      const hasAllTimestamps = finalTrip.loading_start_time && 
                              finalTrip.loading_end_time && 
                              finalTrip.unloading_start_time && 
                              finalTrip.unloading_end_time && 
                              finalTrip.trip_completed_time;
      
      if (hasAllTimestamps) {
        console.log('\n🎉 SUCCESS: Complete trip progression verified!');
        console.log('✅ All timestamps present');
        console.log('✅ All durations calculated');
        console.log('✅ Trip status: trip_completed');
        console.log('\n🚀 The trip progression system is now working correctly!');
      } else {
        console.log('\n❌ INCOMPLETE: Some timestamps are missing');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    client.release();
  }
}

// Run the test
if (require.main === module) {
  testCompleteTripFlow()
    .then(() => {
      console.log('\n✅ Complete trip flow test completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Complete trip flow test failed:', error);
      process.exit(1);
    });
}

module.exports = { testCompleteTripFlow };
