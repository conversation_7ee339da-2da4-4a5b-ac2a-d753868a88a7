-- Replace approvals table with route_changes table
CREATE TABLE route_changes (
  id SERIAL PRIMARY KEY,
  trip_log_id INTEGER REFERENCES trip_logs(id),
  original_assignment_id INTEGER REFERENCES assignments(id),
  new_assignment_id INTEGER REFERENCES assignments(id),
  change_reason TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add status to assignments table
ALTER TABLE assignments ADD COLUMN status VARCHAR(20) 
  CHECK (status IN ('active', 'completed', 'superseded'));